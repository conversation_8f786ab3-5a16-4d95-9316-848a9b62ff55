#!/bin/bash

set -e

# Update system packages (Amazon Linux 2)
yum update -y
yum install -y curl unzip wget

# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf awscliv2.zip aws/

# Install kubectl
KUBECTL_VERSION=$(curl -L -s https://dl.k8s.io/release/stable.txt)
curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl"
curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl.sha256"
echo "$(cat kubectl.sha256)  kubectl" | sha256sum --check
if [[ $? == 0 ]]; then
    install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
    rm -f kubectl kubectl.sha256
fi

# Install Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Install Docker (optional, for container management)
yum install -y docker
systemctl enable docker
systemctl start docker
usermod -a -G docker ec2-user

# Install additional useful tools
yum install -y git htop tree jq

# Log installation completion
echo "Instance setup completed at $(date)" >> /var/log/user-data.log