#!/bin/bash

set -e

# Update system packages (Amazon Linux 2)
yum update -y
yum install -y curl unzip wget git htop tree jq vim nano

# Install AWS CLI v2 (for private VPC endpoints if configured)
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf awscliv2.zip aws/

# Install kubectl for EKS management
KUBECTL_VERSION="v1.28.0"  # Fixed version for consistency in private environment
curl -LO "https://dl.k8s.io/release/${KUBECTL_VERSION}/bin/linux/amd64/kubectl"
install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
rm -f kubectl

# Install Helm for Kubernetes package management
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# Install Session Manager plugin for private access (if needed)
yum install -y https://s3.amazonaws.com/session-manager-downloads/plugin/latest/linux_64bit/session-manager-plugin.rpm

# Install network troubleshooting tools for private infrastructure
yum install -y telnet nc nmap-ncat traceroute bind-utils

# Configure SSH for bastion functionality
echo "# Bastion Host SSH Configuration" >> /etc/ssh/sshd_config
echo "ClientAliveInterval 60" >> /etc/ssh/sshd_config
echo "ClientAliveCountMax 3" >> /etc/ssh/sshd_config
systemctl restart sshd

# Create bastion user for operations
useradd -m -s /bin/bash bastion-ops
mkdir -p /home/<USER>/.ssh
chown bastion-ops:bastion-ops /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh

# Install additional monitoring tools
yum install -y iotop iftop nethogs

# Log installation completion
echo "Private bastion host setup completed at $(date)" >> /var/log/user-data.log
echo "Accessible via Transit Gateway from datacenter" >> /var/log/user-data.log