# Bastion Host Module Outputs (Private Infrastructure)

output "bastion_instance_ids" {
  description = "IDs of the bastion host instances"
  value       = aws_instance.bastion[*].id
}

output "bastion_instance_arns" {
  description = "ARNs of the bastion host instances"
  value       = aws_instance.bastion[*].arn
}

output "bastion_private_ips" {
  description = "Private IP addresses of the bastion host instances (accessible via Transit Gateway)"
  value       = aws_instance.bastion[*].private_ip
}

output "bastion_private_dns" {
  description = "Private DNS names of the bastion host instances"
  value       = aws_instance.bastion[*].private_dns
}

output "bastion_availability_zones" {
  description = "Availability zones where bastion host instances are deployed"
  value       = aws_instance.bastion[*].availability_zone
}

output "bastion_subnet_ids" {
  description = "Subnet IDs where bastion host instances are deployed"
  value       = aws_instance.bastion[*].subnet_id
}

output "bastion_security_group_ids" {
  description = "Security group IDs attached to bastion host instances"
  value       = var.security_group_ids
}

output "ami_id" {
  description = "ID of the AMI used for bastion host instances"
  value       = data.aws_ami.amazon_linux.id
}