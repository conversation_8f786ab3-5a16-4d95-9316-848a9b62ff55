# Bastion Host Module Outputs

output "bastion_instance_ids" {
  description = "IDs of the bastion host instances"
  value       = aws_instance.bastion[*].id
}

output "bastion_instance_arns" {
  description = "ARNs of the bastion host instances"
  value       = aws_instance.bastion[*].arn
}

output "bastion_public_ips" {
  description = "Public IP addresses of the bastion host instances"
  value       = aws_instance.bastion[*].public_ip
}

output "bastion_private_ips" {
  description = "Private IP addresses of the bastion host instances"
  value       = aws_instance.bastion[*].private_ip
}

output "bastion_public_dns" {
  description = "Public DNS names of the bastion host instances"
  value       = aws_instance.bastion[*].public_dns
}

output "bastion_private_dns" {
  description = "Private DNS names of the bastion host instances"
  value       = aws_instance.bastion[*].private_dns
}

output "bastion_security_group_ids" {
  description = "Security group IDs attached to bastion host instances"
  value       = var.security_group_ids
}

output "ami_id" {
  description = "ID of the AMI used for bastion host instances"
  value       = data.aws_ami.amazon_linux.id
}