# Dev Backend Config Output
output "dev_backend_config_hcl" {
  description = "Backend configuration in HCL format for dev environment"
  value = <<-EOT
    bucket         = "${aws_s3_bucket.dev_state.id}"
    key            = "terraform-dev.tfstate"
    dynamodb_table = "${aws_dynamodb_table.dev_lock.name}"
    region         = "${aws_s3_bucket.dev_state.region}"
    encrypt        = true
    use_lockfile   = true
  EOT
}

# Prod Backend Config Output
output "prod_backend_config_hcl" {
  description = "Backend configuration in HCL format for prod environment"
  value = <<-EOT
    bucket         = "${aws_s3_bucket.prod_state.id}"
    key            = "terraform-prod.tfstate"
    dynamodb_table = "${aws_dynamodb_table.prod_lock.name}"
    region         = "${aws_s3_bucket.prod_state.region}"
    encrypt        = true
    use_lockfile   = true
  EOT
} 