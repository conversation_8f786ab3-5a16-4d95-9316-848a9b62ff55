# EKS Module

This module creates an Amazon EKS (Elastic Kubernetes Service) cluster with associated resources. The module is organized into separate files for better maintainability and clarity.

## File Structure

- **main.tf**: Main entry point with documentation references
- **cluster.tf**: EKS cluster configuration and OIDC provider
- **nodes.tf**: EKS node groups configuration
- **iam-roles.tf**: IAM roles and policies for cluster and node groups
- **access-roles.tf**: Developer and manager access roles
- **addons.tf**: EKS addons (Pod Identity, EBS CSI Driver)
- **helm-provider.tf**: Helm and Kubernetes provider configuration
- **variables.tf**: Input variables
- **outputs.tf**: Output values

## Features

### Core EKS Components
- EKS cluster with configurable Kubernetes version
- Managed node groups with auto-scaling
- CloudWatch logging for cluster control plane
- VPC configuration with private/public endpoint options

### Security & Access
- IAM Roles for Service Accounts (IRSA) with OIDC provider
- Developer and manager access roles
- EKS access entries for fine-grained permissions
- Pod Identity addon for secure pod-to-AWS service communication

### Storage & Networking
- EBS CSI driver addon for persistent storage
- Support for EBS encryption
- Configurable node group subnets

### Monitoring & Scaling
- CloudWatch log groups with configurable retention
- Node group auto-scaling configuration
- Support for mixed instance types and capacity types

## Usage

```hcl
module "eks" {
  source = "./modules/eks"

  # Basic Configuration
  cluster_name    = "my-cluster"
  cluster_version = "1.28"
  project_name    = "my-project"
  environment     = "dev"

  # Network Configuration
  vpc_id                 = module.vpc.vpc_id
  subnet_ids             = module.vpc.private_subnet_ids
  node_group_subnet_ids  = module.vpc.private_subnet_ids

  # Access Configuration
  endpoint_private_access = true
  endpoint_public_access  = false

  # Node Groups
  node_groups = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size       = 1
      max_size       = 5
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels = {
        role = "general"
      }
      taints = []
    }
    spot = {
      instance_types = ["t3.medium", "t3.large"]
      capacity_type  = "SPOT"
      min_size       = 0
      max_size       = 10
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels = {
        role = "spot"
      }
      taints = [
        {
          key    = "spot"
          value  = "true"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }

  # Access Management
  create_developer_user = true
  create_manager_role   = true

  # Addons
  enable_pod_identity    = true
  enable_ebs_csi_driver = true
  enable_ebs_encryption = true

  # Logging
  enable_cluster_log_types     = ["api", "audit", "authenticator"]
  cluster_log_retention_days   = 14

  common_tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

## Helm Module Integration

This module can be used with the companion Helm module to install common Kubernetes applications:

```hcl
module "helm" {
  source = "./modules/helm"

  cluster_name               = module.eks.cluster_name
  cluster_endpoint           = module.eks.cluster_endpoint
  cluster_ca_certificate     = module.eks.cluster_certificate_authority_data
  cluster_token              = data.aws_eks_cluster_auth.main.token
  vpc_id                     = module.vpc.vpc_id
  aws_region                 = var.aws_region

  # Enable desired components
  enable_metrics_server              = true
  enable_cluster_autoscaler          = true
  enable_aws_load_balancer_controller = true

  project_name = var.project_name
  environment  = var.environment
  common_tags  = var.common_tags

  depends_on = [module.eks]
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | >= 5.0 |
| tls | >= 4.0 |

## Providers

| Name | Version |
|------|---------|
| aws | >= 5.0 |
| tls | >= 4.0 |

## Inputs

See `variables.tf` for a complete list of input variables.

## Outputs

See `outputs.tf` for a complete list of output values.

## Notes

- The module uses the new EKS access entries API for managing cluster access
- Pod Identity is preferred over IRSA for new deployments
- Node groups support both On-Demand and Spot instances
- All resources are tagged with common tags for cost tracking and management
