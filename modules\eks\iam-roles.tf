# =============================================================================
# IAM ROLES FOR EKS CLUSTER AND NODE GROUPS
# =============================================================================
# This file contains the core IAM roles and policies required for EKS operation
# These roles provide the necessary permissions for the EKS service and worker nodes
# to function properly within AWS

# -----------------------------------------------------------------------------
# DATA SOURCES
# -----------------------------------------------------------------------------
# Get current AWS account ID and partition for constructing ARNs
data "aws_caller_identity" "current" {}
data "aws_partition" "current" {}

# -----------------------------------------------------------------------------
# EKS CLUSTER SERVICE ROLE
# -----------------------------------------------------------------------------
# This role is assumed by the EKS service to manage the cluster control plane
# It allows EKS to create and manage AWS resources on behalf of the cluster
resource "aws_iam_role" "cluster" {
  name = "${var.cluster_name}-cluster-role"

  # Trust policy: Allows EKS service to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          # Only the EKS service can assume this role
          Service = "eks.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-cluster-role"
    }
  )
}

# -----------------------------------------------------------------------------
# EKS CLUSTER POLICY ATTACHMENT
# -----------------------------------------------------------------------------
# Attaches the AWS managed policy that provides necessary permissions for EKS
# This policy includes permissions for:
# - Managing ENIs (Elastic Network Interfaces) for cluster networking
# - Creating and managing security groups
# - Describing VPC resources
# - Managing route tables and subnets
resource "aws_iam_role_policy_attachment" "cluster_AmazonEKSClusterPolicy" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEKSClusterPolicy"
  role       = aws_iam_role.cluster.name
}

# -----------------------------------------------------------------------------
# EKS NODE GROUP SERVICE ROLE
# -----------------------------------------------------------------------------
# This role is assumed by EC2 instances in the node groups
# It provides permissions for worker nodes to join the cluster and function properly
resource "aws_iam_role" "node_group" {
  name = "${var.cluster_name}-node-group-role"

  # Trust policy: Allows EC2 instances to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          # Only EC2 instances can assume this role
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-node-group-role"
    }
  )
}

# -----------------------------------------------------------------------------
# NODE GROUP POLICY ATTACHMENTS
# -----------------------------------------------------------------------------
# These AWS managed policies provide the necessary permissions for worker nodes

# 1. AmazonEKSWorkerNodePolicy
# Allows worker nodes to connect to EKS cluster and register themselves
# Includes permissions for:
# - Describing cluster information
# - Connecting to the cluster API server
resource "aws_iam_role_policy_attachment" "node_group_AmazonEKSWorkerNodePolicy" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = aws_iam_role.node_group.name
}

# 2. AmazonEKS_CNI_Policy
# Provides permissions for the Amazon VPC CNI plugin to manage network interfaces
# Includes permissions for:
# - Creating and managing ENIs (Elastic Network Interfaces)
# - Assigning IP addresses to pods
# - Managing VPC networking for pods
resource "aws_iam_role_policy_attachment" "node_group_AmazonEKS_CNI_Policy" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = aws_iam_role.node_group.name
}

# 3. AmazonEC2ContainerRegistryReadOnly
# Allows worker nodes to pull container images from Amazon ECR
# Includes permissions for:
# - Authenticating with ECR
# - Downloading container images
# - Accessing ECR repositories
resource "aws_iam_role_policy_attachment" "node_group_AmazonEC2ContainerRegistryReadOnly" {
  policy_arn = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = aws_iam_role.node_group.name
}
