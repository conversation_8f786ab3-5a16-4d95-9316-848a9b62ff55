# EKS Modular Terraform Project

This Terraform project provisions a complete AWS network for EKS and Transit Gateway integration using a single, modular `network` module. It follows best practices for maintainability, clarity, and multi-environment support.

## 🚀 Modern Architecture

- **Bootstrap Project**: Separate project for S3 backend and DynamoDB table (see `bootstrap/`)
- **Main Project**: Uses a single `network` module for all VPC, subnets, TGW, and security groups
- **No Circular Dependencies**: Backend is managed separately
- **Workspaces & tfvars**: Use Terraform workspaces and tfvars for dev/prod or other environments

## Project Structure

```
Terraform-EKS-/
├── bootstrap/                # Backend infrastructure (run once)
├── config/                   # Backend config files
├── modules/
│   └── network/              # All VPC, subnet, TGW, and security group logic
├── main.tf                   # Root module, calls network module
├── variables.tf              # Variable definitions
├── outputs.tf                # Output definitions
├── eks.tf                    # EKS cluster and node group
├── bastion.tf                # Bastion host (optional)
├── dev.tfvars                # Dev environment variables
├── production.tfvars         # Prod environment variables
├── provider.tf               # AWS provider config
├── README.md                 # This file
└── ...
```

## Features
- **Single source of truth for all networking**: VPC, subnets, TGW, security groups
- **EKS-ready**: Subnets and security groups are tagged and structured for EKS
- **Multi-environment**: Use workspaces and tfvars for dev/prod
- **Extensible**: Add NAT, endpoints, or more as needed

## Quick Start

### 1. Bootstrap Backend (if not done)
```bash
cd bootstrap
terraform init
terraform apply
```

### 2. Deploy Main Infrastructure
```bash
cd ..
terraform init
terraform workspace select dev   # or prod
terraform plan -var-file=dev.tfvars
terraform apply -var-file=dev.tfvars
```

## Example main.tf
```hcl
module "network" {
  source = "./modules/network"
  vpc_cidr                    = var.vpc_cidr
  availability_zones          = var.availability_zones
  private_eks_subnet_cidrs    = var.private_eks_subnet_cidrs
  private_lb_subnet_cidrs     = var.private_lb_subnet_cidrs
  intra_subnet_cidrs          = var.intra_subnet_cidrs
  tgw_subnet_cidrs            = var.tgw_subnet_cidrs
  map_public_ip_on_launch     = var.map_public_ip_on_launch
  create_transit_gateway_attachment = var.create_transit_gateway_attachment
  existing_transit_gateway_id = var.existing_transit_gateway_id
  enable_dns_support_attachment = var.enable_dns_support_attachment
  enable_ipv6_support_attachment = var.enable_ipv6_support_attachment
  appliance_mode_support         = var.appliance_mode_support
  tags                        = local.common_tags
  project_name                = var.project_name
  environment                 = var.environment
}
```

## Outputs
- `vpc_id`, `private_eks_subnet_ids`, `private_lb_subnet_ids`, `intra_subnet_ids`, `tgw_subnet_ids`
- Security group IDs: `general_use_security_group_id`, `eks_alb_sg_id`, `bastion_sg_id`, `eks_worker_sg_id`
- See `outputs.tf` for the full list

## Environment Management
- Use `terraform workspace select dev` and `-var-file=dev.tfvars` for dev
- Use `terraform workspace select prod` and `-var-file=production.tfvars` for prod

## Documentation
- See [`modules/network/README.md`](modules/network/README.md) for full module documentation and usage
- See [`SETUP_GUIDE.md`](SETUP_GUIDE.md) for step-by-step setup
- See [`SUBNET_ARCHITECTURE.md`](SUBNET_ARCHITECTURE.md) and [`TRANSIT_GATEWAY_SETUP.md`](TRANSIT_GATEWAY_SETUP.md) for design details

## Security & Best Practices
- All subnets are private by default
- Security groups are modular and can be extended
- Use workspaces and tfvars to avoid accidental cross-environment changes

## Support
For issues or questions, see the documentation files or open an issue.
