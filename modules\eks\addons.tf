# EKS Addons Configuration

# Pod Identity Agent Addon
resource "aws_eks_addon" "pod_identity" {
  count         = var.enable_pod_identity ? 1 : 0
  cluster_name  = aws_eks_cluster.main.name
  addon_name    = "eks-pod-identity-agent"
  addon_version = var.pod_identity_addon_version

  depends_on = [aws_eks_node_group.main]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-pod-identity-addon"
    }
  )
}

# EBS CSI Driver Addon
resource "aws_eks_addon" "ebs_csi_driver" {
  count                    = var.enable_ebs_csi_driver ? 1 : 0
  cluster_name             = aws_eks_cluster.main.name
  addon_name               = "aws-ebs-csi-driver"
  addon_version            = var.ebs_csi_driver_addon_version
  service_account_role_arn = var.enable_ebs_csi_driver ? aws_iam_role.ebs_csi_driver[0].arn : null

  depends_on = [aws_eks_node_group.main]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-csi-addon"
    }
  )
}

# EBS CSI Driver IAM Role
resource "aws_iam_role" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0
  name  = "${var.cluster_name}-ebs-csi-driver"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "pods.eks.amazonaws.com"
        }
        Action = [
          "sts:AssumeRole",
          "sts:TagSession"
        ]
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-csi-role"
    }
  )
}

# EBS CSI Driver Policy Attachment
resource "aws_iam_role_policy_attachment" "ebs_csi_driver" {
  count      = var.enable_ebs_csi_driver ? 1 : 0
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
  role       = aws_iam_role.ebs_csi_driver[0].name
}

# EBS CSI Driver Encryption Policy (Optional)
resource "aws_iam_policy" "ebs_csi_driver_encryption" {
  count = var.enable_ebs_csi_driver && var.enable_ebs_encryption ? 1 : 0
  name  = "${var.cluster_name}-ebs-csi-driver-encryption"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKeyWithoutPlaintext",
          "kms:CreateGrant"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-encryption-policy"
    }
  )
}

# EBS CSI Driver Encryption Policy Attachment
resource "aws_iam_role_policy_attachment" "ebs_csi_driver_encryption" {
  count      = var.enable_ebs_csi_driver && var.enable_ebs_encryption ? 1 : 0
  policy_arn = aws_iam_policy.ebs_csi_driver_encryption[0].arn
  role       = aws_iam_role.ebs_csi_driver[0].name
}

# EBS CSI Driver Pod Identity Association
resource "aws_eks_pod_identity_association" "ebs_csi_driver" {
  count           = var.enable_ebs_csi_driver && var.enable_pod_identity ? 1 : 0
  cluster_name    = aws_eks_cluster.main.name
  namespace       = "kube-system"
  service_account = "ebs-csi-controller-sa"
  role_arn        = aws_iam_role.ebs_csi_driver[0].arn

  depends_on = [aws_eks_addon.pod_identity]
}
