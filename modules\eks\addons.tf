# =============================================================================
# EKS ADDONS CONFIGURATION
# =============================================================================
# This file manages EKS addons, which are AWS-managed Kubernetes components
# Addons provide essential functionality like storage drivers and pod authentication
# They are automatically updated and maintained by AWS

# -----------------------------------------------------------------------------
# POD IDENTITY ADDON
# -----------------------------------------------------------------------------
# Pod Identity is AWS's newer approach for pods to authenticate with AWS services
# It's simpler and more secure than IRSA (IAM Roles for Service Accounts)
# This addon installs the Pod Identity Agent on worker nodes

resource "aws_eks_addon" "pod_identity" {
  count = var.enable_pod_identity ? 1 : 0

  cluster_name  = aws_eks_cluster.main.name
  addon_name    = "eks-pod-identity-agent"  # AWS-managed addon name
  addon_version = var.pod_identity_addon_version

  # Ensure node groups exist before installing addon
  depends_on = [aws_eks_node_group.main]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-pod-identity-addon"
    }
  )
}

# -----------------------------------------------------------------------------
# EBS CSI DRIVER ADDON
# -----------------------------------------------------------------------------
# The EBS CSI (Container Storage Interface) driver allows Kubernetes to manage
# Amazon EBS volumes as persistent storage for pods
# This is essential for stateful applications that need persistent storage

resource "aws_eks_addon" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0

  cluster_name  = aws_eks_cluster.main.name
  addon_name    = "aws-ebs-csi-driver"  # AWS-managed addon name
  addon_version = var.ebs_csi_driver_addon_version

  # IAM role that the EBS CSI driver will use to manage EBS volumes
  # This role needs permissions to create, attach, detach, and delete EBS volumes
  service_account_role_arn = var.enable_ebs_csi_driver ? aws_iam_role.ebs_csi_driver[0].arn : null

  # Ensure node groups exist before installing addon
  depends_on = [aws_eks_node_group.main]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-csi-addon"
    }
  )
}

# -----------------------------------------------------------------------------
# EBS CSI DRIVER IAM ROLE AND PERMISSIONS
# -----------------------------------------------------------------------------
# The EBS CSI driver needs AWS permissions to manage EBS volumes
# This section creates the necessary IAM role and policies

# EBS CSI Driver IAM Role
# This role is used by the EBS CSI driver pods to authenticate with AWS
resource "aws_iam_role" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0
  name  = "${var.cluster_name}-ebs-csi-driver"

  # Trust policy for Pod Identity
  # This allows pods in EKS to assume this role using Pod Identity
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          # Pod Identity service principal
          Service = "pods.eks.amazonaws.com"
        }
        Action = [
          "sts:AssumeRole",
          "sts:TagSession"  # Required for Pod Identity
        ]
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-csi-role"
    }
  )
}

# EBS CSI Driver Policy Attachment
# Attaches the AWS managed policy that provides EBS management permissions
resource "aws_iam_role_policy_attachment" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0

  # AWS managed policy with permissions for EBS operations:
  # - CreateVolume, DeleteVolume, AttachVolume, DetachVolume
  # - CreateSnapshot, DeleteSnapshot
  # - DescribeVolumes, DescribeSnapshots, etc.
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEBSCSIDriverPolicy"
  role       = aws_iam_role.ebs_csi_driver[0].name
}

# EBS CSI Driver Encryption Policy (Optional)
# Additional permissions for encrypted EBS volumes
resource "aws_iam_policy" "ebs_csi_driver_encryption" {
  count = var.enable_ebs_csi_driver && var.enable_ebs_encryption ? 1 : 0
  name  = "${var.cluster_name}-ebs-csi-driver-encryption"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          # Required for working with encrypted EBS volumes
          "kms:Decrypt",                        # Decrypt existing volumes
          "kms:GenerateDataKeyWithoutPlaintext", # Create new encrypted volumes
          "kms:CreateGrant"                     # Grant permissions for volume operations
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-encryption-policy"
    }
  )
}

# EBS CSI Driver Encryption Policy Attachment
resource "aws_iam_role_policy_attachment" "ebs_csi_driver_encryption" {
  count      = var.enable_ebs_csi_driver && var.enable_ebs_encryption ? 1 : 0
  policy_arn = aws_iam_policy.ebs_csi_driver_encryption[0].arn
  role       = aws_iam_role.ebs_csi_driver[0].name
}

# -----------------------------------------------------------------------------
# POD IDENTITY ASSOCIATION FOR EBS CSI DRIVER
# -----------------------------------------------------------------------------
# This associates the IAM role with the EBS CSI driver service account
# When Pod Identity is enabled, this allows the CSI driver pods to assume the IAM role

resource "aws_eks_pod_identity_association" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver && var.enable_pod_identity ? 1 : 0

  cluster_name    = aws_eks_cluster.main.name
  namespace       = "kube-system"                    # EBS CSI driver runs in kube-system
  service_account = "ebs-csi-controller-sa"          # Service account used by EBS CSI driver
  role_arn        = aws_iam_role.ebs_csi_driver[0].arn

  # Ensure Pod Identity addon is installed first
  depends_on = [aws_eks_addon.pod_identity]
}
