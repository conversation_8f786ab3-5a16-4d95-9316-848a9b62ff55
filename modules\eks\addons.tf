# =============================================================================
# EKS ADDONS CONFIGURATION
# =============================================================================
# This file manages EKS addons, which are AWS-managed Kubernetes components
# Addons provide essential functionality like storage drivers and pod authentication
# They are automatically updated and maintained by AWS

# -----------------------------------------------------------------------------
# POD IDENTITY ADDON
# -----------------------------------------------------------------------------
# Pod Identity is AWS's newer approach for pods to authenticate with AWS services
# It's simpler and more secure than IRSA (IAM Roles for Service Accounts)
# This addon installs the Pod Identity Agent on worker nodes

resource "aws_eks_addon" "pod_identity" {
  count = var.enable_pod_identity ? 1 : 0

  cluster_name  = aws_eks_cluster.main.name
  addon_name    = "eks-pod-identity-agent"  # AWS-managed addon name
  addon_version = var.pod_identity_addon_version

  # Ensure node groups exist before installing addon
  depends_on = [aws_eks_node_group.main]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-pod-identity-addon"
    }
  )
}

# -----------------------------------------------------------------------------
# EBS CSI DRIVER ADDON
# -----------------------------------------------------------------------------
# The EBS CSI (Container Storage Interface) driver allows Kubernetes to manage
# Amazon EBS volumes as persistent storage for pods
# This is essential for stateful applications that need persistent storage

resource "aws_eks_addon" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver ? 1 : 0

  cluster_name  = aws_eks_cluster.main.name
  addon_name    = "aws-ebs-csi-driver"  # AWS-managed addon name
  addon_version = var.ebs_csi_driver_addon_version

  # IAM role that the EBS CSI driver will use to manage EBS volumes
  # This role needs permissions to create, attach, detach, and delete EBS volumes
  service_account_role_arn = var.enable_ebs_csi_driver ? aws_iam_role.ebs_csi_driver[0].arn : null

  # Ensure node groups exist before installing addon
  depends_on = [aws_eks_node_group.main]

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-ebs-csi-addon"
    }
  )
}

# -----------------------------------------------------------------------------
# EBS CSI DRIVER CONFIGURATION
# -----------------------------------------------------------------------------
# The EBS CSI driver needs AWS permissions to manage EBS volumes
# The IAM role is now created by the Identity module and passed as a variable

# -----------------------------------------------------------------------------
# POD IDENTITY ASSOCIATION FOR EBS CSI DRIVER
# -----------------------------------------------------------------------------
# This associates the IAM role with the EBS CSI driver service account
# When Pod Identity is enabled, this allows the CSI driver pods to assume the IAM role
# The IAM role is created by the Identity module and passed as a variable

resource "aws_eks_pod_identity_association" "ebs_csi_driver" {
  count = var.enable_ebs_csi_driver && var.enable_pod_identity ? 1 : 0

  cluster_name    = aws_eks_cluster.main.name
  namespace       = "kube-system"                    # EBS CSI driver runs in kube-system
  service_account = "ebs-csi-controller-sa"          # Service account used by EBS CSI driver
  role_arn        = var.ebs_csi_driver_role_arn      # Role ARN from Identity module

  # Ensure Pod Identity addon is installed first
  depends_on = [aws_eks_addon.pod_identity]
}

# =============================================================================
# EFS (ELASTIC FILE SYSTEM) CONFIGURATION
# =============================================================================
# EFS provides shared, persistent storage that can be mounted by multiple pods
# Unlike EBS (block storage), EFS is a network file system that supports concurrent access

# -----------------------------------------------------------------------------
# EFS FILE SYSTEM
# -----------------------------------------------------------------------------
# Creates the EFS file system that will be used by Kubernetes pods

resource "aws_efs_file_system" "eks" {
  count = var.enable_efs ? 1 : 0

  creation_token = "${var.project_name}-${var.environment}-eks-efs"

  performance_mode = var.efs_performance_mode  # generalPurpose or maxIO
  throughput_mode  = var.efs_throughput_mode   # bursting or provisioned
  encrypted        = var.efs_encrypted         # Enable encryption at rest

  # Optional lifecycle policy to transition files to Infrequent Access storage class
  dynamic "lifecycle_policy" {
    for_each = var.efs_transition_to_ia != null ? [1] : []
    content {
      transition_to_ia = var.efs_transition_to_ia
    }
  }

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-efs"
      Type = "efs-file-system"
    }
  )
}

# -----------------------------------------------------------------------------
# EFS MOUNT TARGETS
# -----------------------------------------------------------------------------
# Mount targets allow EC2 instances in specific subnets to access the EFS file system
# We create one mount target per private subnet for high availability

resource "aws_efs_mount_target" "eks" {
  count = var.enable_efs ? length(var.private_subnet_ids) : 0

  file_system_id  = aws_efs_file_system.eks[0].id
  subnet_id       = var.private_subnet_ids[count.index]
  security_groups = [aws_eks_cluster.main.vpc_config[0].cluster_security_group_id]

  depends_on = [aws_efs_file_system.eks]
}

# -----------------------------------------------------------------------------
# EFS CSI DRIVER CONFIGURATION
# -----------------------------------------------------------------------------
# The EFS CSI driver needs AWS permissions to manage EFS access points and mount targets
# The IAM role is now created by the Identity module and passed as a variable

# -----------------------------------------------------------------------------
# POD IDENTITY ASSOCIATION FOR EFS CSI DRIVER
# -----------------------------------------------------------------------------
# This associates the IAM role with the EFS CSI driver service account
# When Pod Identity is enabled, this allows the CSI driver pods to assume the IAM role
# The IAM role is created by the Identity module and passed as a variable

resource "aws_eks_pod_identity_association" "efs_csi_driver" {
  count = var.enable_efs && var.enable_pod_identity ? 1 : 0

  cluster_name    = aws_eks_cluster.main.name
  namespace       = "kube-system"                    # EFS CSI driver runs in kube-system
  service_account = "efs-csi-controller-sa"          # Service account used by EFS CSI driver
  role_arn        = var.efs_csi_driver_role_arn      # Role ARN from Identity module

  # Ensure Pod Identity addon is installed first
  depends_on = [aws_eks_addon.pod_identity]
}

# -----------------------------------------------------------------------------
# EFS CSI DRIVER HELM INSTALLATION
# -----------------------------------------------------------------------------
# Install the EFS CSI driver using Helm chart
# The EFS CSI driver is not available as an AWS managed addon, so we use Helm

resource "helm_release" "efs_csi_driver" {
  count = var.enable_efs ? 1 : 0

  name       = "aws-efs-csi-driver"
  repository = "https://kubernetes-sigs.github.io/aws-efs-csi-driver/"
  chart      = "aws-efs-csi-driver"
  namespace  = "kube-system"
  version    = var.efs_csi_driver_chart_version

  set {
    name  = "controller.serviceAccount.name"
    value = "efs-csi-controller-sa"
  }

  # Set the IAM role annotation for IRSA or leave empty for Pod Identity
  dynamic "set" {
    for_each = var.enable_irsa && !var.enable_pod_identity ? [1] : []
    content {
      name  = "controller.serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
      value = var.efs_csi_driver_role_arn  # Role ARN from Identity module
    }
  }

  # Ensure mount targets are created before installing the driver
  depends_on = [
    aws_efs_mount_target.eks
  ]
}

# -----------------------------------------------------------------------------
# EFS STORAGE CLASS
# -----------------------------------------------------------------------------
# Creates a Kubernetes storage class for EFS volumes
# This allows pods to request EFS storage using PersistentVolumeClaims

resource "kubernetes_storage_class_v1" "efs" {
  count = var.enable_efs ? 1 : 0

  metadata {
    name = "efs"
    annotations = {
      "storageclass.kubernetes.io/is-default-class" = var.efs_set_default_storage_class ? "true" : "false"
    }
  }

  storage_provisioner = "efs.csi.aws.com"

  parameters = {
    provisioningMode = "efs-ap"                           # Use EFS Access Points
    fileSystemId     = aws_efs_file_system.eks[0].id     # Reference to our EFS file system
    directoryPerms   = var.efs_directory_perms            # Directory permissions (e.g., "700")
  }

  # Mount options for EFS
  mount_options = var.efs_mount_options

  # Allow volume expansion
  allow_volume_expansion = true

  # Reclaim policy - what happens to the volume when PVC is deleted
  reclaim_policy = var.efs_reclaim_policy

  # Volume binding mode
  volume_binding_mode = "Immediate"

  depends_on = [helm_release.efs_csi_driver]
}
