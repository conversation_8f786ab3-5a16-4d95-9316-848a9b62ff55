# =============================================================================
# OIDC MODULE VARIABLES
# =============================================================================
# This file defines all input variables for the OIDC module

# -----------------------------------------------------------------------------
# BASIC CONFIGURATION
# -----------------------------------------------------------------------------

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# -----------------------------------------------------------------------------
# OIDC CONFIGURATION
# -----------------------------------------------------------------------------

variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts (IRSA) by creating OIDC provider"
  type        = bool
  default     = true
}

variable "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer (from EKS cluster)"
  type        = string
  
  validation {
    condition     = can(regex("^https://", var.cluster_oidc_issuer_url))
    error_message = "The cluster_oidc_issuer_url must be a valid HTTPS URL."
  }
}

# -----------------------------------------------------------------------------
# VALIDATION RULES
# -----------------------------------------------------------------------------

# Ensure OIDC issuer URL is provided when IRSA is enabled
validation {
  condition     = var.enable_irsa ? var.cluster_oidc_issuer_url != null && var.cluster_oidc_issuer_url != "" : true
  error_message = "cluster_oidc_issuer_url must be provided when enable_irsa is true."
}
