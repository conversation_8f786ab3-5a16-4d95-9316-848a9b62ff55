# =============================================================================
# HELM MODULE PROVIDERS CONFIGURATION
# =============================================================================
# This file configures the Helm and Kubernetes providers for the Helm module
# These providers are separate from the EKS module to maintain proper
# separation of concerns and avoid provider conflicts

# -----------------------------------------------------------------------------
# TERRAFORM PROVIDER REQUIREMENTS
# -----------------------------------------------------------------------------
# Specifies the required provider versions for this module
terraform {
  required_providers {
    # Helm provider for deploying Helm charts
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.12"  # Use latest 2.x version for stability
    }
    # Kubernetes provider for direct K8s API operations
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.24"  # Compatible with recent Kubernetes versions
    }
  }
}

# -----------------------------------------------------------------------------
# HELM PROVIDER CONFIGURATION
# -----------------------------------------------------------------------------
# The Helm provider is used to deploy Helm charts to the Kubernetes cluster
# It connects to the EKS cluster using credentials passed from the EKS module
provider "helm" {
  kubernetes {
    # EKS cluster API server endpoint URL
    # Example: https://ABC123.gr7.us-west-2.eks.amazonaws.com
    host = var.cluster_endpoint

    # Base64-decoded cluster CA certificate for TLS verification
    # This ensures secure communication with the cluster API server
    cluster_ca_certificate = base64decode(var.cluster_ca_certificate)

    # Authentication token obtained from AWS EKS
    # Generated using aws_eks_cluster_auth data source in the calling module
    # Token is temporary and automatically refreshed by the AWS provider
    token = var.cluster_token
  }
}

# -----------------------------------------------------------------------------
# KUBERNETES PROVIDER CONFIGURATION
# -----------------------------------------------------------------------------
# The Kubernetes provider is used for direct Kubernetes API operations
# Some Helm charts may require additional Kubernetes resources (RBAC, etc.)
provider "kubernetes" {
  # Same connection configuration as Helm provider for consistency
  host                   = var.cluster_endpoint
  cluster_ca_certificate = base64decode(var.cluster_ca_certificate)
  token                  = var.cluster_token
}
