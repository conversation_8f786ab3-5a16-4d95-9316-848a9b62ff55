# EKS Module Main Configuration
# This file now serves as the main entry point and includes references to other files
# The actual resources are organized in separate files for better maintainability:
# - cluster.tf: EKS cluster and OIDC provider
# - nodes.tf: EKS node groups
# - iam-roles.tf: IAM roles and policies
# - access-roles.tf: Developer and manager access roles
# - addons.tf: EKS addons (Pod Identity, EBS CSI Driver)
# - helm-provider.tf: Helm and Kubernetes provider configuration

resource "aws_iam_openid_connect_provider" "eks" {
  count = var.enable_irsa ? 1 : 0

  client_id_list  = ["sts.amazonaws.com"]
  thumbprint_list = [data.tls_certificate.eks[0].certificates[0].sha1_fingerprint]
  url             = aws_eks_cluster.main.identity[0].oidc[0].issuer

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-oidc-provider"
    }
  )
}
