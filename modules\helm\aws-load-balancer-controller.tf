# =============================================================================
# AWS LOAD BALANCER CONTROLLER HELM CHART
# =============================================================================
# AWS Load Balancer Controller manages AWS Elastic Load Balancers for Kubernetes
# clusters. It provisions Application Load Balancers (ALB) for Ingress resources
# and Network Load Balancers (NLB) for LoadBalancer services.
#
# Key Features:
# - Automatic ALB provisioning for Kubernetes Ingress resources
# - NLB support for LoadBalancer services with advanced features
# - Integration with AWS WAF, ACM certificates, and Target Groups
# - Support for both public and private load balancers
# - Advanced routing capabilities (host-based, path-based)
# - WebSocket and gRPC support

# -----------------------------------------------------------------------------
# AWS LOAD BALANCER CONTROLLER DEPLOYMENT
# -----------------------------------------------------------------------------
resource "helm_release" "aws_load_balancer_controller" {
  count = var.enable_aws_load_balancer_controller ? 1 : 0

  # Basic Helm release configuration
  name       = "aws-load-balancer-controller"
  repository = "https://aws.github.io/eks-charts"  # Official AWS EKS charts repository
  chart      = "aws-load-balancer-controller"
  namespace  = "kube-system"  # Deploy in system namespace
  version    = var.aws_load_balancer_controller_version

  # Cluster identification for load balancer tagging and management
  set {
    name  = "clusterName"
    value = var.cluster_name
  }

  # Service Account Configuration
  # Creates a Kubernetes service account with IAM role annotation for AWS permissions
  set {
    name  = "serviceAccount.name"
    value = "aws-load-balancer-controller"
  }

  # IAM Role Configuration
  # Annotates the service account with IAM role ARN for AWS permissions
  # This role must have permissions to manage ALBs, NLBs, and related resources
  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = var.aws_load_balancer_controller_role_arn
  }

  # VPC Configuration
  # Specifies the VPC where load balancers will be created
  # Required for proper subnet selection and security group management
  set {
    name  = "vpcId"
    value = var.vpc_id
  }

  # AWS Region Configuration
  # Required for AWS API calls to manage load balancers
  set {
    name  = "region"
    value = var.aws_region
  }

  # Detailed configuration using values block
  values = [
    yamlencode({
      # High availability configuration with 2 replicas
      # Ensures load balancer management continues if one pod fails
      replicaCount = 2

      # Resource limits and requests
      # Controller needs more resources than typical system components
      # due to AWS API calls and webhook processing
      resources = {
        limits = {
          cpu    = "200m"   # Maximum CPU usage (0.2 CPU cores)
          memory = "500Mi"  # Maximum memory usage (500 MiB)
        }
        requests = {
          cpu    = "100m"   # Guaranteed CPU allocation (0.1 CPU cores)
          memory = "200Mi"  # Guaranteed memory allocation (200 MiB)
        }
      }

      # Node selector ensures controller runs only on Linux nodes
      # Critical in mixed-OS clusters (Linux + Windows)
      nodeSelector = {
        "kubernetes.io/os" = "linux"
      }

      # Tolerations allow the pod to be scheduled on master/control plane nodes
      # This ensures the controller can run even if worker nodes are unavailable
      tolerations = [
        {
          key      = "node-role.kubernetes.io/master"  # Master node taint
          operator = "Exists"                          # Tolerate any value for the taint key
          effect   = "NoSchedule"                      # Allow scheduling despite NoSchedule taint
        }
      ]

      # Pod Disruption Budget ensures at least one replica is always available
      # This prevents service disruption during node maintenance or updates
      podDisruptionBudget = {
        maxUnavailable = 1  # Allow maximum 1 pod to be unavailable
      }

      # Environment variables for AWS configuration
      # Ensures consistent AWS region configuration across all operations
      env = {
        AWS_DEFAULT_REGION = var.aws_region
      }
    })
  ]

  # Ensure cluster autoscaler is deployed first to avoid resource conflicts
  depends_on = [helm_release.cluster_autoscaler]
}
