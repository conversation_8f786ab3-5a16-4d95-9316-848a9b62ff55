# =============================================================================
# CLUSTER AUTOSCALER HELM CHART
# =============================================================================
# Cluster Autoscaler automatically adjusts the size of EKS node groups based on
# pod resource demands. It scales up when pods can't be scheduled due to resource
# constraints and scales down when nodes are underutilized.
#
# Key Features:
# - Automatic node group scaling based on pod demands
# - Multiple scaling strategies (least-waste, priority, etc.)
# - Integration with AWS Auto Scaling Groups
# - Respects pod disruption budgets and node taints
# - Prevents scaling down nodes with local storage or system pods

# -----------------------------------------------------------------------------
# CLUSTER AUTOSCALER DEPLOYMENT
# -----------------------------------------------------------------------------
resource "helm_release" "cluster_autoscaler" {
  count = var.enable_cluster_autoscaler ? 1 : 0

  # Basic Helm release configuration
  name       = "cluster-autoscaler"
  repository = "https://kubernetes.github.io/autoscaler"  # Official Kubernetes autoscaler repository
  chart      = "cluster-autoscaler"
  namespace  = "kube-system"  # Deploy in system namespace
  version    = var.cluster_autoscaler_version

  # Service Account Configuration
  # Creates a Kubernetes service account with IAM role annotation for AWS permissions
  set {
    name  = "rbac.serviceAccount.name"
    value = "cluster-autoscaler"
  }

  # Auto-discovery Configuration
  # Enables automatic discovery of Auto Scaling Groups by cluster name
  # ASGs must be tagged with: k8s.io/cluster-autoscaler/enabled=true and k8s.io/cluster-autoscaler/CLUSTER_NAME=owned
  set {
    name  = "autoDiscovery.clusterName"
    value = var.cluster_name
  }

  # AWS Region Configuration
  # Required for AWS API calls to manage Auto Scaling Groups
  set {
    name  = "awsRegion"
    value = var.aws_region
  }

  # IAM Role Configuration
  # Annotates the service account with IAM role ARN for AWS permissions
  # This role must have permissions to describe and modify Auto Scaling Groups
  set {
    name  = "rbac.serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = var.cluster_autoscaler_role_arn
  }

  # Detailed configuration using values block
  values = [
    yamlencode({
      # Single replica for cluster autoscaler (should not be scaled horizontally)
      replicaCount = 1

      # Container image configuration
      # Pin to specific version for stability and compatibility with EKS version
      image = {
        tag = "v1.28.2"  # Should match or be compatible with EKS cluster version
      }

      # Resource limits and requests
      # Ensures predictable performance and prevents resource starvation
      resources = {
        limits = {
          cpu    = "100m"   # Maximum CPU usage (0.1 CPU cores)
          memory = "600Mi"  # Maximum memory usage (600 MiB)
        }
        requests = {
          cpu    = "100m"   # Guaranteed CPU allocation (0.1 CPU cores)
          memory = "600Mi"  # Guaranteed memory allocation (600 MiB)
        }
      }

      # Command line arguments for cluster autoscaler
      extraArgs = {
        "v"                                 = 4                    # Verbose logging level (1-10)
        "stderrthreshold"                  = "info"               # Log level threshold
        "cloud-provider"                   = "aws"                # AWS cloud provider
        "skip-nodes-with-local-storage"    = false                # Allow scaling down nodes with local storage
        "expander"                         = "least-waste"        # Scaling strategy to minimize resource waste
        "node-group-auto-discovery"        = "asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/${var.cluster_name}"  # Auto-discover ASGs by tags
        "balance-similar-node-groups"      = false                # Don't balance between similar node groups
        "skip-nodes-with-system-pods"      = false                # Allow scaling down nodes with system pods
      }

      # Node selector ensures cluster-autoscaler runs only on Linux nodes
      # Critical in mixed-OS clusters (Linux + Windows)
      nodeSelector = {
        "kubernetes.io/os" = "linux"
      }

      # Tolerations allow the pod to be scheduled on master/control plane nodes
      # This ensures cluster-autoscaler can run even if worker nodes are unavailable
      tolerations = [
        {
          key      = "node-role.kubernetes.io/master"  # Master node taint
          operator = "Exists"                          # Tolerate any value for the taint key
          effect   = "NoSchedule"                      # Allow scheduling despite NoSchedule taint
        }
      ]
    })
  ]

  # Ensure metrics server is deployed first as cluster autoscaler may use its metrics
  depends_on = [helm_release.metrics_server]
}
