# Cluster Autoscaler Helm Chart

resource "helm_release" "cluster_autoscaler" {
  count = var.enable_cluster_autoscaler ? 1 : 0

  name       = "cluster-autoscaler"
  repository = "https://kubernetes.github.io/autoscaler"
  chart      = "cluster-autoscaler"
  namespace  = "kube-system"
  version    = var.cluster_autoscaler_version

  set {
    name  = "rbac.serviceAccount.name"
    value = "cluster-autoscaler"
  }

  set {
    name  = "autoDiscovery.clusterName"
    value = var.cluster_name
  }

  set {
    name  = "awsRegion"
    value = var.aws_region
  }

  set {
    name  = "rbac.serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = var.cluster_autoscaler_role_arn
  }

  values = [
    yamlencode({
      replicaCount = 1
      
      image = {
        tag = "v1.28.2"
      }

      resources = {
        limits = {
          cpu    = "100m"
          memory = "600Mi"
        }
        requests = {
          cpu    = "100m"
          memory = "600Mi"
        }
      }

      extraArgs = {
        "v"                                 = 4
        "stderrthreshold"                  = "info"
        "cloud-provider"                   = "aws"
        "skip-nodes-with-local-storage"    = false
        "expander"                         = "least-waste"
        "node-group-auto-discovery"        = "asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/${var.cluster_name}"
        "balance-similar-node-groups"      = false
        "skip-nodes-with-system-pods"      = false
      }

      nodeSelector = {
        "kubernetes.io/os" = "linux"
      }

      tolerations = [
        {
          key      = "node-role.kubernetes.io/master"
          operator = "Exists"
          effect   = "NoSchedule"
        }
      ]
    })
  ]

  depends_on = [helm_release.metrics_server]
}
