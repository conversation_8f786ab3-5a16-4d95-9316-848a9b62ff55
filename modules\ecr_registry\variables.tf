# =============================================================================
# ECR REGISTRY MODULE VARIABLES
# =============================================================================
# This file defines all input variables for the ECR registry module

# -----------------------------------------------------------------------------
# BASIC CONFIGURATION
# -----------------------------------------------------------------------------

variable "project_name" {
  description = "Name of the project"
  type        = string
}

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# -----------------------------------------------------------------------------
# REPOSITORY CONFIGURATION
# -----------------------------------------------------------------------------

variable "repository_names" {
  description = "List of ECR repository names to create (without project/environment prefix)"
  type        = list(string)
  default = [
    "app-frontend",
    "app-backend",
    "app-api",
    "nginx",
    "redis"
  ]
  
  validation {
    condition     = length(var.repository_names) > 0
    error_message = "At least one repository name must be specified."
  }
}

variable "image_tag_mutability" {
  description = "The tag mutability setting for the repository (MUTABLE or IMMUTABLE)"
  type        = string
  default     = "MUTABLE"
  
  validation {
    condition     = contains(["MUTABLE", "IMMUTABLE"], var.image_tag_mutability)
    error_message = "image_tag_mutability must be either MUTABLE or IMMUTABLE."
  }
}

# -----------------------------------------------------------------------------
# SECURITY CONFIGURATION
# -----------------------------------------------------------------------------

variable "enable_image_scanning" {
  description = "Enable image vulnerability scanning on push"
  type        = bool
  default     = true
}

variable "encryption_type" {
  description = "The encryption type to use for the repository (AES256 or KMS)"
  type        = string
  default     = "AES256"
  
  validation {
    condition     = contains(["AES256", "KMS"], var.encryption_type)
    error_message = "encryption_type must be either AES256 or KMS."
  }
}

variable "kms_key_id" {
  description = "The KMS key ID to use for encryption (required when encryption_type is KMS)"
  type        = string
  default     = null
}

# -----------------------------------------------------------------------------
# LIFECYCLE POLICY CONFIGURATION
# -----------------------------------------------------------------------------

variable "enable_lifecycle_policy" {
  description = "Enable lifecycle policy for automatic image cleanup"
  type        = bool
  default     = true
}

variable "max_image_count" {
  description = "Maximum number of images to keep per repository"
  type        = number
  default     = 10
  
  validation {
    condition     = var.max_image_count > 0
    error_message = "max_image_count must be greater than 0."
  }
}

variable "untagged_image_days" {
  description = "Number of days to keep untagged images before deletion"
  type        = number
  default     = 7
  
  validation {
    condition     = var.untagged_image_days > 0
    error_message = "untagged_image_days must be greater than 0."
  }
}

variable "lifecycle_tag_prefixes" {
  description = "List of tag prefixes to apply lifecycle policy to"
  type        = list(string)
  default     = ["v", "release", "main", "develop"]
}

# -----------------------------------------------------------------------------
# CROSS-ACCOUNT ACCESS CONFIGURATION
# -----------------------------------------------------------------------------

variable "enable_cross_account_access" {
  description = "Enable cross-account access to ECR repositories"
  type        = bool
  default     = false
}

variable "cross_account_arns" {
  description = "List of AWS account ARNs allowed to pull images (when cross-account access is enabled)"
  type        = list(string)
  default     = []
}

# Note: Validation rules are handled within individual variable declarations
