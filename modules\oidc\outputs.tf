# =============================================================================
# OIDC MODULE OUTPUTS
# =============================================================================
# This file defines all output values from the OIDC module

# -----------------------------------------------------------------------------
# OIDC PROVIDER OUTPUTS
# -----------------------------------------------------------------------------

output "provider_arn" {
  description = "ARN of the OIDC Provider for IRSA"
  value       = var.enable_irsa ? aws_iam_openid_connect_provider.eks[0].arn : null
}

output "provider_url" {
  description = "URL of the OIDC Provider"
  value       = var.enable_irsa ? aws_iam_openid_connect_provider.eks[0].url : null
}

output "issuer_url" {
  description = "OIDC issuer URL without https:// prefix (for use in IAM trust policies)"
  value       = var.enable_irsa ? replace(var.cluster_oidc_issuer_url, "https://", "") : null
}

output "thumbprint" {
  description = "Thumbprint of the OIDC provider certificate"
  value       = var.enable_irsa ? data.tls_certificate.eks[0].certificates[0].sha1_fingerprint : null
}

# -----------------------------------------------------------------------------
# INTEGRATION OUTPUTS
# -----------------------------------------------------------------------------

output "enabled" {
  description = "Whether IRSA/OIDC provider is enabled"
  value       = var.enable_irsa
}

output "trust_policy_condition" {
  description = "Common condition block for IAM trust policies using this OIDC provider"
  value = var.enable_irsa ? {
    StringEquals = {
      "${replace(var.cluster_oidc_issuer_url, "https://", "")}:aud" = "sts.amazonaws.com"
    }
  } : null
}

# -----------------------------------------------------------------------------
# SUMMARY OUTPUT
# -----------------------------------------------------------------------------

output "oidc_provider_details" {
  description = "Complete OIDC provider information for integration"
  value = var.enable_irsa ? {
    arn         = aws_iam_openid_connect_provider.eks[0].arn
    url         = aws_iam_openid_connect_provider.eks[0].url
    issuer_url  = replace(var.cluster_oidc_issuer_url, "https://", "")
    thumbprint  = data.tls_certificate.eks[0].certificates[0].sha1_fingerprint
    enabled     = true
  } : {
    arn         = null
    url         = null
    issuer_url  = null
    thumbprint  = null
    enabled     = false
  }
}
