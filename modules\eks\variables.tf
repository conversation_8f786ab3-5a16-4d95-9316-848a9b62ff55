# =============================================================================
# EKS MODULE VARIABLES
# =============================================================================

# -----------------------------------------------------------------------------
# IAM ROLE ARNS FROM IDENTITY MODULE
# -----------------------------------------------------------------------------
# These variables receive IAM role ARNs from the Identity module
# The Identity module creates all IAM roles and policies

variable "cluster_service_role_arn" {
  description = "ARN of the EKS cluster service role from Identity module"
  type        = string
}

variable "node_group_role_arn" {
  description = "ARN of the EKS node group role from Identity module"
  type        = string
}

variable "ebs_csi_driver_role_arn" {
  description = "ARN of the EBS CSI driver role from Identity module"
  type        = string
  default     = null
}

variable "efs_csi_driver_role_arn" {
  description = "ARN of the EFS CSI driver role from Identity module"
  type        = string
  default     = null
}

# -----------------------------------------------------------------------------
# USER ACCESS CONFIGURATION
# -----------------------------------------------------------------------------
# These variables control user access entries and receive user/role ARNs from Identity module

variable "create_developer_user" {
  description = "Whether to create developer access entry"
  type        = bool
  default     = false
}

variable "create_manager_role" {
  description = "Whether to create manager access entry"
  type        = bool
  default     = false
}

variable "developer_user_arn" {
  description = "ARN of the developer IAM user from Identity module"
  type        = string
  default     = null
}

variable "eks_admin_role_arn" {
  description = "ARN of the EKS admin role from Identity module"
  type        = string
  default     = null
}

variable "developer_kubernetes_groups" {
  description = "Kubernetes groups for developer access"
  type        = list(string)
  default     = ["my-viewer"]
}

variable "manager_kubernetes_groups" {
  description = "Kubernetes groups for manager access"
  type        = list(string)
  default     = ["my-admin"]
}

# -----------------------------------------------------------------------------
# CLUSTER CONFIGURATION
# -----------------------------------------------------------------------------

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "vpc_id" {
  description = "VPC ID where EKS cluster will be created"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for EKS cluster"
  type        = list(string)
}

variable "node_group_subnet_ids" {
  description = "List of subnet IDs for EKS node groups"
  type        = list(string)
}

variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "enable_cluster_log_types" {
  description = "List of control plane logging types to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "cluster_log_retention_days" {
  description = "Number of days to retain cluster logs"
  type        = number
  default     = 7
}

variable "node_groups" {
  description = "Map of EKS node group configurations"
  type = map(object({
    instance_types = list(string)
    capacity_type  = string
    min_size       = number
    max_size       = number
    desired_size   = number
    disk_size      = number
    ami_type       = string
    labels         = map(string)
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
  }))
  default = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size       = 1
      max_size       = 3
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels         = {}
      taints         = []
    }
  }
}

variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts"
  type        = bool
  default     = true
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

# Cluster Access Configuration
variable "authentication_mode" {
  description = "Authentication mode for the cluster. Valid values are CONFIG_MAP, API or API_AND_CONFIG_MAP"
  type        = string
  default     = "API"
}

variable "bootstrap_cluster_creator_admin_permissions" {
  description = "Bootstrap cluster creator admin permissions"
  type        = bool
  default     = true
}

# Developer and Manager Access
variable "create_developer_user" {
  description = "Create developer IAM user and access"
  type        = bool
  default     = false
}

variable "create_manager_role" {
  description = "Create manager IAM role and access"
  type        = bool
  default     = false
}

variable "developer_kubernetes_groups" {
  description = "Kubernetes groups for developer access"
  type        = list(string)
  default     = ["my-viewer"]
}

variable "manager_kubernetes_groups" {
  description = "Kubernetes groups for manager access"
  type        = list(string)
  default     = ["my-admin"]
}

# Pod Identity Configuration
variable "enable_pod_identity" {
  description = "Enable EKS Pod Identity addon"
  type        = bool
  default     = true
}

variable "pod_identity_addon_version" {
  description = "Version of the EKS Pod Identity addon"
  type        = string
  default     = "v1.2.0-eksbuild.1"
}

# EBS CSI Driver Configuration
variable "enable_ebs_csi_driver" {
  description = "Enable EBS CSI driver addon"
  type        = bool
  default     = true
}

variable "ebs_csi_driver_addon_version" {
  description = "Version of the EBS CSI driver addon"
  type        = string
  default     = "v1.31.0-eksbuild.1"
}

variable "enable_ebs_encryption" {
  description = "Enable EBS encryption support for CSI driver"
  type        = bool
  default     = true
}

# =============================================================================
# EFS (ELASTIC FILE SYSTEM) CONFIGURATION
# =============================================================================

variable "enable_efs" {
  description = "Enable EFS file system and CSI driver"
  type        = bool
  default     = false
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs for EFS mount targets"
  type        = list(string)
  default     = []
}

variable "efs_performance_mode" {
  description = "EFS performance mode (generalPurpose or maxIO)"
  type        = string
  default     = "generalPurpose"

  validation {
    condition     = contains(["generalPurpose", "maxIO"], var.efs_performance_mode)
    error_message = "EFS performance mode must be either 'generalPurpose' or 'maxIO'."
  }
}

variable "efs_throughput_mode" {
  description = "EFS throughput mode (bursting or provisioned)"
  type        = string
  default     = "bursting"

  validation {
    condition     = contains(["bursting", "provisioned"], var.efs_throughput_mode)
    error_message = "EFS throughput mode must be either 'bursting' or 'provisioned'."
  }
}

variable "efs_encrypted" {
  description = "Enable EFS encryption at rest"
  type        = bool
  default     = true
}

variable "efs_transition_to_ia" {
  description = "Lifecycle policy transition to Infrequent Access (AFTER_7_DAYS, AFTER_14_DAYS, AFTER_30_DAYS, AFTER_60_DAYS, AFTER_90_DAYS)"
  type        = string
  default     = null

  validation {
    condition = var.efs_transition_to_ia == null || contains([
      "AFTER_7_DAYS", "AFTER_14_DAYS", "AFTER_30_DAYS",
      "AFTER_60_DAYS", "AFTER_90_DAYS"
    ], var.efs_transition_to_ia)
    error_message = "EFS transition to IA must be one of: AFTER_7_DAYS, AFTER_14_DAYS, AFTER_30_DAYS, AFTER_60_DAYS, AFTER_90_DAYS, or null."
  }
}

variable "efs_csi_driver_chart_version" {
  description = "Version of the EFS CSI driver Helm chart"
  type        = string
  default     = "3.0.5"
}

variable "efs_set_default_storage_class" {
  description = "Set EFS storage class as default"
  type        = bool
  default     = false
}

variable "efs_directory_perms" {
  description = "Directory permissions for EFS access points"
  type        = string
  default     = "700"
}

variable "efs_mount_options" {
  description = "Mount options for EFS volumes"
  type        = list(string)
  default     = ["iam"]
}

variable "efs_reclaim_policy" {
  description = "Reclaim policy for EFS volumes (Retain, Delete)"
  type        = string
  default     = "Retain"

  validation {
    condition     = contains(["Retain", "Delete"], var.efs_reclaim_policy)
    error_message = "EFS reclaim policy must be either 'Retain' or 'Delete'."
  }
}
