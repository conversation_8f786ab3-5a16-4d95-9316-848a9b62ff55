# EKS Module Variables

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "vpc_id" {
  description = "VPC ID where EKS cluster will be created"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for EKS cluster"
  type        = list(string)
}

variable "node_group_subnet_ids" {
  description = "List of subnet IDs for EKS node groups"
  type        = list(string)
}

variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "enable_cluster_log_types" {
  description = "List of control plane logging types to enable"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "cluster_log_retention_days" {
  description = "Number of days to retain cluster logs"
  type        = number
  default     = 7
}

variable "node_groups" {
  description = "Map of EKS node group configurations"
  type = map(object({
    instance_types = list(string)
    capacity_type  = string
    min_size       = number
    max_size       = number
    desired_size   = number
    disk_size      = number
    ami_type       = string
    labels         = map(string)
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
  }))
  default = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size       = 1
      max_size       = 3
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels         = {}
      taints         = []
    }
  }
}

variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts"
  type        = bool
  default     = true
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

# Cluster Access Configuration
variable "authentication_mode" {
  description = "Authentication mode for the cluster. Valid values are CONFIG_MAP, API or API_AND_CONFIG_MAP"
  type        = string
  default     = "API"
}

variable "bootstrap_cluster_creator_admin_permissions" {
  description = "Bootstrap cluster creator admin permissions"
  type        = bool
  default     = true
}

# Developer and Manager Access
variable "create_developer_user" {
  description = "Create developer IAM user and access"
  type        = bool
  default     = false
}

variable "create_manager_role" {
  description = "Create manager IAM role and access"
  type        = bool
  default     = false
}

variable "developer_kubernetes_groups" {
  description = "Kubernetes groups for developer access"
  type        = list(string)
  default     = ["my-viewer"]
}

variable "manager_kubernetes_groups" {
  description = "Kubernetes groups for manager access"
  type        = list(string)
  default     = ["my-admin"]
}

# Pod Identity Configuration
variable "enable_pod_identity" {
  description = "Enable EKS Pod Identity addon"
  type        = bool
  default     = true
}

variable "pod_identity_addon_version" {
  description = "Version of the EKS Pod Identity addon"
  type        = string
  default     = "v1.2.0-eksbuild.1"
}

# EBS CSI Driver Configuration
variable "enable_ebs_csi_driver" {
  description = "Enable EBS CSI driver addon"
  type        = bool
  default     = true
}

variable "ebs_csi_driver_addon_version" {
  description = "Version of the EBS CSI driver addon"
  type        = string
  default     = "v1.31.0-eksbuild.1"
}

variable "enable_ebs_encryption" {
  description = "Enable EBS encryption support for CSI driver"
  type        = bool
  default     = true
}
