# Root main.tf - Calls modules and defines locals
locals {
  workspace = terraform.workspace
  common_tags = merge(
    var.common_tags,
    {
      Environment = local.workspace
      Project     = var.project_name
      ManagedBy   = "Terraform"
    }
  )
}

# Data Sources
data "aws_caller_identity" "current" {}

# Network Module
module "network" {
  source = "./modules/network"

  vpc_cidr                    = var.vpc_cidr
  availability_zones          = var.availability_zones
  private_eks_subnet_cidrs    = var.private_eks_subnet_cidrs
  private_lb_subnet_cidrs     = var.private_lb_subnet_cidrs
  intra_subnet_cidrs          = var.intra_subnet_cidrs
  tgw_subnet_cidrs            = var.tgw_subnet_cidrs
  map_public_ip_on_launch     = var.map_public_ip_on_launch
  create_transit_gateway_attachment = var.create_transit_gateway_attachment
  existing_transit_gateway_id = var.existing_transit_gateway_id
  enable_dns_support_attachment = var.enable_dns_support_attachment
  enable_ipv6_support_attachment = var.enable_ipv6_support_attachment
  appliance_mode_support         = var.appliance_mode_support
  common_tags                        = local.common_tags
  project_name                = var.project_name
  environment                 = var.environment
}

# Bastion Host Module (Private Infrastructure)
module "compute" {
  count  = var.enable_bastion_host ? 1 : 0
  source = "./modules/compute"

  vpc_id             = module.network.vpc_id
  subnet_ids         = module.network.intra_subnet_ids  # Using intra subnets for fully private bastion
  security_group_ids = [module.network.bastion_sg_id]

  instance_type               = var.bastion_instance_type
  instance_count              = var.bastion_instance_count
  key_name                   = var.bastion_key_name
  associate_public_ip_address = var.bastion_associate_public_ip  # Will be false for private infrastructure
  root_volume_size           = var.bastion_root_volume_size

  common_tags    = local.common_tags
  project_name   = var.project_name
  environment    = var.environment
}

# EKS Module
module "eks" {
  count  = var.enable_eks_module ? 1 : 0
  source = "./modules/eks"

  cluster_name    = var.eks_cluster_name != null ? var.eks_cluster_name : "${var.project_name}-${var.environment}-eks"
  cluster_version = var.eks_cluster_version

  vpc_id                = module.network.vpc_id
  subnet_ids            = module.network.private_eks_subnet_ids
  node_group_subnet_ids = module.network.private_eks_subnet_ids

  endpoint_private_access      = var.eks_endpoint_private_access
  endpoint_public_access       = var.eks_endpoint_public_access
  endpoint_public_access_cidrs = var.eks_endpoint_public_access_cidrs

  enable_cluster_log_types     = var.eks_enable_cluster_log_types
  cluster_log_retention_days   = var.eks_cluster_log_retention_days

  node_groups  = var.eks_node_groups
  enable_irsa  = var.eks_enable_irsa

  common_tags  = local.common_tags
  project_name = var.project_name
  environment  = var.environment
}