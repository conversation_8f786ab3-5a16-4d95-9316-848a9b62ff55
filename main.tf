# Root main.tf - Calls modules and defines locals
locals {
  workspace = terraform.workspace
  common_tags = merge(
    var.common_tags,
    {
      Environment = local.workspace
      Project     = var.project_name
      ManagedBy   = "Terraform"
    }
  )
}

# Data Sources
data "aws_caller_identity" "current" {}

# Security Module (SSH Key Pairs)
module "security" {
  count  = var.enable_compute_module ? 1 : 0
  source = "./modules/security"

  project_name           = var.project_name
  environment           = var.environment
  create_local_key_file = var.create_local_key_files
  key_algorithm         = var.bastion_key_algorithm
  rsa_bits              = var.bastion_rsa_bits

  common_tags = local.common_tags
}

# Network Module
module "network" {
  count  = var.enable_network_module ? 1 : 0
  source = "./modules/network"

  vpc_cidr                    = var.vpc_cidr
  availability_zones          = var.availability_zones
  private_eks_subnet_cidrs    = var.private_eks_subnet_cidrs
  private_lb_subnet_cidrs     = var.private_lb_subnet_cidrs
  intra_subnet_cidrs          = var.intra_subnet_cidrs
  tgw_subnet_cidrs            = var.tgw_subnet_cidrs
  map_public_ip_on_launch     = var.map_public_ip_on_launch
  create_transit_gateway_attachment = var.create_transit_gateway_attachment
  existing_transit_gateway_id = var.existing_transit_gateway_id
  enable_dns_support_attachment = var.enable_dns_support_attachment
  enable_ipv6_support_attachment = var.enable_ipv6_support_attachment
  appliance_mode_support         = var.appliance_mode_support
  common_tags                        = local.common_tags
  project_name                = var.project_name
  environment                 = var.environment
}

# Compute Module (Bastion Hosts)
module "compute" {
  count  = var.enable_compute_module ? 1 : 0
  source = "./modules/compute"

  vpc_id             = module.network[0].vpc_id
  subnet_ids         = module.network[0].intra_subnet_ids  # Using intra subnets for fully private bastion
  security_group_ids = [module.network[0].bastion_sg_id]

  instance_type               = var.bastion_instance_type
  instance_count              = var.bastion_instance_count
  key_name                   = module.security[0].bastion_key_pair_name
  associate_public_ip_address = var.bastion_associate_public_ip  # Will be false for private infrastructure
  root_volume_size           = var.bastion_root_volume_size

  common_tags    = local.common_tags
  project_name   = var.project_name
  environment    = var.environment
}

# Identity Module (IAM Roles and Policies)
module "identity" {
  count  = var.enable_eks_module ? 1 : 0
  source = "./modules/identity"

  # Basic Configuration
  project_name = var.project_name
  environment  = var.environment
  cluster_name = var.eks_cluster_name != null ? var.eks_cluster_name : "${var.project_name}-${var.environment}-eks"

  # CSI Driver Configuration
  enable_ebs_csi_driver = var.eks_enable_ebs_csi_driver
  enable_efs           = var.enable_efs

  # OIDC Configuration
  enable_pod_identity = var.eks_enable_pod_identity
  # Note: OIDC provider ARN and issuer URL will be passed separately after OIDC module is created

  # Access Configuration
  create_developer_user = var.eks_create_developer_user
  create_manager_role   = var.eks_create_manager_role

  common_tags = local.common_tags
}

# OIDC Module (OpenID Connect Provider for IRSA)
module "oidc" {
  count  = var.enable_eks_module && var.eks_enable_irsa && !var.eks_enable_pod_identity ? 1 : 0
  source = "./modules/oidc"

  # Basic Configuration
  project_name = var.project_name
  environment  = var.environment

  # OIDC Configuration
  enable_irsa              = var.eks_enable_irsa
  cluster_oidc_issuer_url  = module.eks[0].cluster_oidc_issuer_url

  common_tags = local.common_tags

  depends_on = [module.eks]
}

# ECR Registry Module (Container Registries)
module "ecr_registry" {
  count  = var.enable_eks_module && var.enable_ecr_registry ? 1 : 0
  source = "./modules/ecr_registry"

  # Basic Configuration
  project_name = var.project_name
  environment  = var.environment

  # Repository Configuration
  repository_names      = var.ecr_repository_names
  image_tag_mutability = var.ecr_image_tag_mutability

  # Security Configuration
  enable_image_scanning = var.ecr_enable_image_scanning
  encryption_type      = var.ecr_encryption_type
  kms_key_id          = var.ecr_kms_key_id

  # Lifecycle Configuration
  enable_lifecycle_policy = var.ecr_enable_lifecycle_policy
  max_image_count        = var.ecr_max_image_count
  untagged_image_days    = var.ecr_untagged_image_days
  lifecycle_tag_prefixes = var.ecr_lifecycle_tag_prefixes

  # Cross-account Configuration
  enable_cross_account_access = var.ecr_enable_cross_account_access
  cross_account_arns         = var.ecr_cross_account_arns

  common_tags = local.common_tags
}

# EKS Module
module "eks" {
  count  = var.enable_eks_module ? 1 : 0
  source = "./modules/eks"

  # IAM Role ARNs from Identity Module
  cluster_service_role_arn = module.identity[0].cluster_service_role_arn
  node_group_role_arn     = module.identity[0].node_group_role_arn
  ebs_csi_driver_role_arn = var.eks_enable_ebs_csi_driver ? module.identity[0].ebs_csi_driver_role_arn : null
  efs_csi_driver_role_arn = var.enable_efs ? module.identity[0].efs_csi_driver_role_arn : null

  # User Access Configuration
  create_developer_user    = var.eks_create_developer_user
  create_manager_role      = var.eks_create_manager_role
  developer_user_arn       = var.eks_create_developer_user ? module.identity[0].developer_user_arn : null
  eks_admin_role_arn       = var.eks_create_manager_role ? module.identity[0].eks_admin_role_arn : null
  developer_kubernetes_groups = var.eks_developer_kubernetes_groups
  manager_kubernetes_groups   = var.eks_manager_kubernetes_groups

  cluster_name    = var.eks_cluster_name != null ? var.eks_cluster_name : "${var.project_name}-${var.environment}-eks"
  cluster_version = var.eks_cluster_version

  vpc_id                = module.network[0].vpc_id
  subnet_ids            = module.network[0].private_eks_subnet_ids
  node_group_subnet_ids = module.network[0].private_eks_subnet_ids

  endpoint_private_access      = var.eks_endpoint_private_access
  endpoint_public_access       = var.eks_endpoint_public_access
  endpoint_public_access_cidrs = var.eks_endpoint_public_access_cidrs

  enable_cluster_log_types     = var.eks_enable_cluster_log_types
  cluster_log_retention_days   = var.eks_cluster_log_retention_days

  node_groups  = var.eks_node_groups
  enable_irsa  = var.eks_enable_irsa

  # EFS Configuration
  enable_efs                      = var.enable_efs
  private_subnet_ids              = module.network[0].private_eks_subnet_ids
  efs_performance_mode            = var.efs_performance_mode
  efs_throughput_mode             = var.efs_throughput_mode
  efs_encrypted                   = var.efs_encrypted
  efs_transition_to_ia            = var.efs_transition_to_ia
  efs_csi_driver_chart_version    = var.efs_csi_driver_chart_version
  efs_set_default_storage_class   = var.efs_set_default_storage_class
  efs_directory_perms             = var.efs_directory_perms
  efs_mount_options               = var.efs_mount_options
  efs_reclaim_policy              = var.efs_reclaim_policy

  common_tags  = local.common_tags
  project_name = var.project_name
  environment  = var.environment

  # Dependencies
  depends_on = [module.identity]
}

# EKS Cluster Authentication Data Source for Helm Module
data "aws_eks_cluster_auth" "main" {
  count = var.enable_helm_module ? 1 : 0
  name  = module.eks[0].cluster_name
}

# Helm Module
module "helm" {
  count  = var.enable_helm_module ? 1 : 0
  source = "./modules/helm"

  # Cluster connection information (from EKS module)
  cluster_name           = module.eks[0].cluster_name
  cluster_endpoint       = module.eks[0].cluster_endpoint
  cluster_ca_certificate = module.eks[0].cluster_certificate_authority_data
  cluster_token          = data.aws_eks_cluster_auth.main[0].token

  # AWS and project configuration
  vpc_id       = module.network[0].vpc_id
  aws_region   = var.aws_region
  project_name = var.project_name
  environment  = var.environment
  common_tags  = local.common_tags

  # Core components configuration
  enable_metrics_server     = var.enable_metrics_server
  metrics_server_version    = var.metrics_server_version

  enable_cluster_autoscaler = var.enable_cluster_autoscaler
  cluster_autoscaler_version = var.cluster_autoscaler_version
  cluster_autoscaler_role_arn = var.cluster_autoscaler_role_arn

  enable_aws_load_balancer_controller = var.enable_aws_load_balancer_controller
  aws_load_balancer_controller_version = var.aws_load_balancer_controller_version
  aws_load_balancer_controller_role_arn = var.aws_load_balancer_controller_role_arn

  # Optional components configuration
  enable_nginx_ingress = var.enable_nginx_ingress
  nginx_ingress_version = var.nginx_ingress_version

  enable_cert_manager = var.enable_cert_manager
  cert_manager_version = var.cert_manager_version

  # EFS is now handled in the EKS module

  enable_secrets_store_csi_driver = var.enable_secrets_store_csi_driver
  secrets_store_csi_driver_version = var.secrets_store_csi_driver_version
  secrets_store_csi_driver_aws_provider_version = var.secrets_store_csi_driver_aws_provider_version

  # Dependency to ensure EKS cluster is ready before deploying Helm charts
  depends_on = [module.eks]
}