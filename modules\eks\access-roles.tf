# EKS Access Roles - Dev<PERSON><PERSON> and Manager Roles

# Developer IAM User
resource "aws_iam_user" "developer" {
  count = var.create_developer_user ? 1 : 0
  name  = "${var.cluster_name}-developer"

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer"
    }
  )
}

# Developer EKS Policy
resource "aws_iam_policy" "developer_eks" {
  count = var.create_developer_user ? 1 : 0
  name  = "${var.cluster_name}-developer-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "eks:DescribeCluster",
          "eks:ListClusters"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer-policy"
    }
  )
}

# Attach developer policy to user
resource "aws_iam_user_policy_attachment" "developer_eks" {
  count      = var.create_developer_user ? 1 : 0
  user       = aws_iam_user.developer[0].name
  policy_arn = aws_iam_policy.developer_eks[0].arn
}

# Developer EKS Access Entry
resource "aws_eks_access_entry" "developer" {
  count             = var.create_developer_user ? 1 : 0
  cluster_name      = aws_eks_cluster.main.name
  principal_arn     = aws_iam_user.developer[0].arn
  kubernetes_groups = var.developer_kubernetes_groups
}

# EKS Admin Role for Managers
resource "aws_iam_role" "eks_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-eks-admin"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "sts:AssumeRole"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-admin-role"
    }
  )
}

# EKS Admin Policy
resource "aws_iam_policy" "eks_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-eks-admin-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "eks:*"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = "iam:PassRole"
        Resource = "*"
        Condition = {
          StringEquals = {
            "iam:PassedToService" = "eks.amazonaws.com"
          }
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-admin-policy"
    }
  )
}

# Attach admin policy to admin role
resource "aws_iam_role_policy_attachment" "eks_admin" {
  count      = var.create_manager_role ? 1 : 0
  role       = aws_iam_role.eks_admin[0].name
  policy_arn = aws_iam_policy.eks_admin[0].arn
}

# Manager IAM User
resource "aws_iam_user" "manager" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-manager"

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-manager"
    }
  )
}

# Policy to allow manager to assume admin role
resource "aws_iam_policy" "eks_assume_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-assume-admin-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource = aws_iam_role.eks_admin[0].arn
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-assume-admin-policy"
    }
  )
}

# Attach assume admin policy to manager
resource "aws_iam_user_policy_attachment" "manager" {
  count      = var.create_manager_role ? 1 : 0
  user       = aws_iam_user.manager[0].name
  policy_arn = aws_iam_policy.eks_assume_admin[0].arn
}

# Manager EKS Access Entry (using admin role)
resource "aws_eks_access_entry" "manager" {
  count             = var.create_manager_role ? 1 : 0
  cluster_name      = aws_eks_cluster.main.name
  principal_arn     = aws_iam_role.eks_admin[0].arn
  kubernetes_groups = var.manager_kubernetes_groups
}
