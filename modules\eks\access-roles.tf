# =============================================================================
# EKS ACCESS ROLES - DEVELOPER AND MANAGER ROLES
# =============================================================================
# This file manages user access to the EKS cluster using the new EKS Access Entries API
# It creates IAM users/roles and maps them to Kubernetes RBAC groups
# This replaces the traditional aws-auth ConfigMap approach

# -----------------------------------------------------------------------------
# DEVELOPER ACCESS CONFIGURATION
# -----------------------------------------------------------------------------
# Creates a developer IAM user with limited EKS permissions
# Developers typically have read-only or limited write access to specific namespaces

# Developer IAM User
# This user represents a developer who needs access to the EKS cluster
resource "aws_iam_user" "developer" {
  count = var.create_developer_user ? 1 : 0
  name  = "${var.cluster_name}-developer"

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer"
    }
  )
}

# Developer EKS Policy
# Minimal AWS permissions needed to interact with EKS cluster
# This only allows describing the cluster - actual Kubernetes permissions
# are managed through RBAC and the access entry below
resource "aws_iam_policy" "developer_eks" {
  count = var.create_developer_user ? 1 : 0
  name  = "${var.cluster_name}-developer-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          # Required to get cluster information for kubectl configuration
          "eks:DescribeCluster",
          # Useful for listing available clusters
          "eks:ListClusters"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-developer-policy"
    }
  )
}

# Attach developer policy to user
resource "aws_iam_user_policy_attachment" "developer_eks" {
  count      = var.create_developer_user ? 1 : 0
  user       = aws_iam_user.developer[0].name
  policy_arn = aws_iam_policy.developer_eks[0].arn
}

# Developer EKS Access Entry
# This is the new way to grant cluster access (replaces aws-auth ConfigMap)
# Maps the IAM user to Kubernetes groups for RBAC
resource "aws_eks_access_entry" "developer" {
  count         = var.create_developer_user ? 1 : 0
  cluster_name  = aws_eks_cluster.main.name
  principal_arn = aws_iam_user.developer[0].arn

  # Kubernetes groups that this user will be a member of
  # These groups should have corresponding ClusterRoles/Roles bound to them
  # Example: "my-viewer" group might be bound to a read-only ClusterRole
  kubernetes_groups = var.developer_kubernetes_groups
}

# -----------------------------------------------------------------------------
# MANAGER ACCESS CONFIGURATION
# -----------------------------------------------------------------------------
# Creates a manager role with elevated EKS permissions
# Managers typically have admin access to the cluster and can manage resources
# This uses a role-based approach for better security (temporary credentials)

# EKS Admin Role for Managers
# This role provides elevated permissions for cluster management
# Managers assume this role when they need admin access
resource "aws_iam_role" "eks_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-eks-admin"

  # Trust policy: Allows users in this AWS account to assume the role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = "sts:AssumeRole"
        Principal = {
          # Allow any user in this AWS account to assume the role
          # In practice, only specific users will have permission to assume it
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-admin-role"
    }
  )
}

# EKS Admin Policy
# Comprehensive permissions for EKS cluster management
resource "aws_iam_policy" "eks_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-eks-admin-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        # Full EKS permissions for cluster management
        Action = [
          "eks:*"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        # Required for EKS operations that need to pass IAM roles
        Action = "iam:PassRole"
        Resource = "*"
        Condition = {
          StringEquals = {
            # Only allow passing roles to EKS service
            "iam:PassedToService" = "eks.amazonaws.com"
          }
        }
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-admin-policy"
    }
  )
}

# Attach admin policy to admin role
resource "aws_iam_role_policy_attachment" "eks_admin" {
  count      = var.create_manager_role ? 1 : 0
  role       = aws_iam_role.eks_admin[0].name
  policy_arn = aws_iam_policy.eks_admin[0].arn
}

# Manager IAM User
# This user represents a manager who needs admin access to the cluster
resource "aws_iam_user" "manager" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-manager"

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-manager"
    }
  )
}

# Policy to allow manager to assume admin role
# This gives the manager user permission to assume the admin role
resource "aws_iam_policy" "eks_assume_admin" {
  count = var.create_manager_role ? 1 : 0
  name  = "${var.cluster_name}-assume-admin-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        # Allow assuming the specific admin role created above
        Resource = aws_iam_role.eks_admin[0].arn
      }
    ]
  })

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-assume-admin-policy"
    }
  )
}

# Attach assume admin policy to manager
resource "aws_iam_user_policy_attachment" "manager" {
  count      = var.create_manager_role ? 1 : 0
  user       = aws_iam_user.manager[0].name
  policy_arn = aws_iam_policy.eks_assume_admin[0].arn
}

# Manager EKS Access Entry (using admin role)
# Maps the admin role (not the user directly) to Kubernetes groups
# This means when the manager assumes the admin role, they get these Kubernetes permissions
resource "aws_eks_access_entry" "manager" {
  count         = var.create_manager_role ? 1 : 0
  cluster_name  = aws_eks_cluster.main.name
  principal_arn = aws_iam_role.eks_admin[0].arn  # Note: Using role ARN, not user ARN

  # Kubernetes groups for admin access
  # Example: "my-admin" group might be bound to cluster-admin ClusterRole
  kubernetes_groups = var.manager_kubernetes_groups
}
