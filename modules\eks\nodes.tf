# =============================================================================
# EKS NODE GROUPS CONFIGURATION
# =============================================================================
# This file contains the configuration for EKS managed node groups
# Node groups are collections of EC2 instances that serve as worker nodes
# for the Kubernetes cluster, running your application pods

# -----------------------------------------------------------------------------
# EKS MANAGED NODE GROUPS
# -----------------------------------------------------------------------------
# Creates managed node groups based on the configuration map provided
# Each node group can have different instance types, scaling, and configuration
resource "aws_eks_node_group" "main" {
  # Create one node group for each entry in var.node_groups map
  for_each = var.node_groups

  # Basic node group configuration
  cluster_name    = aws_eks_cluster.main.name
  node_group_name = "${var.cluster_name}-${each.key}"  # e.g., "my-cluster-general"
  node_role_arn   = var.node_group_role_arn           # IAM role for worker nodes from Identity module
  subnet_ids      = var.node_group_subnet_ids         # Subnets for worker nodes
  version         = var.cluster_version               # Kubernetes version for nodes

  # Instance configuration
  # capacity_type: ON_DEMAND (guaranteed) or SPOT (cheaper but can be interrupted)
  capacity_type  = each.value.capacity_type
  # instance_types: List of EC2 instance types (e.g., ["t3.medium", "t3.large"])
  instance_types = each.value.instance_types
  # ami_type: Amazon Machine Image type (AL2_x86_64, AL2_x86_64_GPU, AL2_ARM_64, etc.)
  ami_type       = each.value.ami_type
  # disk_size: Root volume size in GB for each worker node
  disk_size      = each.value.disk_size

  # Auto Scaling configuration
  # Controls how many worker nodes are running in this node group
  scaling_config {
    desired_size = each.value.desired_size  # Target number of nodes
    max_size     = each.value.max_size      # Maximum nodes (for scale-out)
    min_size     = each.value.min_size      # Minimum nodes (for scale-in)
  }

  # Update configuration for rolling updates
  # Controls how many nodes can be unavailable during updates
  update_config {
    # Use the configured value or default to 1 if not specified
    max_unavailable = try(each.value.max_unavailable, 1)
  }

  # Kubernetes labels applied to all nodes in this group
  # These can be used for pod scheduling and node selection
  # Example: nodeSelector in pod spec can target specific labels
  labels = each.value.labels

  # Kubernetes taints applied to nodes in this group
  # Taints prevent pods from being scheduled unless they have matching tolerations
  # Useful for dedicated node groups (e.g., GPU nodes, spot instances)
  dynamic "taint" {
    for_each = each.value.taints
    content {
      key    = taint.value.key      # Taint key (e.g., "spot")
      value  = taint.value.value    # Taint value (e.g., "true")
      effect = taint.value.effect   # Effect: NoSchedule, PreferNoSchedule, or NoExecute
    }
  }

  # Ensure IAM policies are attached before creating node group
  depends_on = [
    aws_iam_role_policy_attachment.node_group_AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.node_group_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.node_group_AmazonEC2ContainerRegistryReadOnly,
  ]

  # Lifecycle management
  # Ignore changes to desired_size to allow external scaling (e.g., Cluster Autoscaler)
  # This prevents Terraform from reverting scaling decisions made by Kubernetes
  lifecycle {
    ignore_changes = [scaling_config[0].desired_size]
  }

  tags = merge(
    var.common_tags,
    {
      Name = "${var.project_name}-${var.environment}-eks-node-group-${each.key}"
    }
  )
}
