# Security Module Outputs
output "bastion_key_pair_name" {
  description = "Name of the bastion host key pair"
  value       = var.enable_compute_module ? module.security[0].bastion_key_pair_name : null
}

output "bastion_key_pair_id" {
  description = "ID of the bastion host key pair"
  value       = var.enable_compute_module ? module.security[0].bastion_key_pair_id : null
}

output "bastion_private_key_ssm_parameter" {
  description = "SSM parameter name containing the private key"
  value       = var.enable_compute_module ? module.security[0].bastion_private_key_ssm_parameter_name : null
}

output "bastion_public_key_ssm_parameter" {
  description = "SSM parameter name containing the public key"
  value       = var.enable_compute_module ? module.security[0].bastion_public_key_ssm_parameter_name : null
}

output "bastion_private_key_file_path" {
  description = "Path to local private key file (if created)"
  value       = var.enable_compute_module ? module.security[0].bastion_private_key_file_path : null
}

# Network Module Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = var.enable_network_module ? module.network[0].vpc_id : null
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = var.vpc_cidr
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = var.enable_network_module ? "arn:aws:ec2:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vpc/${module.network[0].vpc_id}" : null
}

output "tgw_attachment_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = var.enable_network_module ? module.network[0].tgw_subnet_ids : []
}

output "private_eks_subnet_ids" {
  description = "IDs of the private EKS subnets"
  value       = var.enable_network_module ? module.network[0].private_eks_subnet_ids : []
}

output "private_lb_subnet_ids" {
  description = "IDs of the private load balancer subnets"
  value       = var.enable_network_module ? module.network[0].private_lb_subnet_ids : []
}

output "intra_subnet_ids" {
  description = "IDs of the intra (isolated) subnets"
  value       = var.enable_network_module ? module.network[0].intra_subnet_ids : []
}

output "all_subnet_ids" {
  description = "All subnet IDs (TGW + Private EKS + Private LB + Intra)"
  value       = var.enable_network_module ? concat(
    module.network[0].tgw_subnet_ids,
    module.network[0].private_eks_subnet_ids,
    module.network[0].private_lb_subnet_ids,
    module.network[0].intra_subnet_ids
  ) : []
}

output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = var.enable_network_module ? module.network[0].general_use_security_group_id : null
}

output "bastion_sg_id" {
  description = "ID of the bastion security group"
  value       = var.enable_network_module ? module.network[0].bastion_sg_id : null
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = var.availability_zones
}

# Transit Gateway Outputs (for existing TGW)
output "existing_transit_gateway_id" {
  description = "ID of the existing Transit Gateway being used"
  value       = var.existing_transit_gateway_id
}

# Compute Module Outputs (Bastion Hosts)
output "bastion_instance_ids" {
  description = "IDs of the bastion host instances"
  value       = var.enable_compute_module ? module.compute[0].bastion_instance_ids : []
}

output "bastion_private_ips" {
  description = "Private IP addresses of the bastion host instances (accessible via Transit Gateway)"
  value       = var.enable_compute_module ? module.compute[0].bastion_private_ips : []
}

output "bastion_private_dns" {
  description = "Private DNS names of the bastion host instances"
  value       = var.enable_compute_module ? module.compute[0].bastion_private_dns : []
}

# =============================================================================
# IDENTITY MODULE OUTPUTS
# =============================================================================

output "cluster_service_role_arn" {
  description = "ARN of the EKS cluster service role"
  value       = var.enable_eks_module ? module.identity[0].cluster_service_role_arn : null
}

output "node_group_role_arn" {
  description = "ARN of the EKS node group role"
  value       = var.enable_eks_module ? module.identity[0].node_group_role_arn : null
}

output "ebs_csi_driver_role_arn" {
  description = "ARN of the EBS CSI driver role"
  value       = var.enable_eks_module && var.eks_enable_ebs_csi_driver ? module.identity[0].ebs_csi_driver_role_arn : null
}

output "efs_csi_driver_role_arn" {
  description = "ARN of the EFS CSI driver role"
  value       = var.enable_eks_module && var.enable_efs ? module.identity[0].efs_csi_driver_role_arn : null
}

output "developer_user_arn" {
  description = "ARN of the developer IAM user"
  value       = var.enable_eks_module && var.eks_create_developer_user ? module.identity[0].developer_user_arn : null
}

output "eks_admin_role_arn" {
  description = "ARN of the EKS admin role"
  value       = var.enable_eks_module && var.eks_create_manager_role ? module.identity[0].eks_admin_role_arn : null
}

# =============================================================================
# OIDC MODULE OUTPUTS
# =============================================================================

output "oidc_provider_arn" {
  description = "ARN of the OIDC provider"
  value       = var.enable_eks_module && var.eks_enable_irsa && !var.eks_enable_pod_identity ? module.oidc[0].provider_arn : null
}

output "oidc_issuer_url" {
  description = "URL of the OIDC issuer"
  value       = var.enable_eks_module && var.eks_enable_irsa && !var.eks_enable_pod_identity ? module.oidc[0].issuer_url : null
}

# =============================================================================
# ECR REGISTRY MODULE OUTPUTS
# =============================================================================

output "ecr_repository_urls" {
  description = "Map of ECR repository URLs"
  value       = var.enable_eks_module && var.enable_ecr_registry ? module.ecr_registry[0].repository_urls : {}
}

output "ecr_repository_arns" {
  description = "Map of ECR repository ARNs"
  value       = var.enable_eks_module && var.enable_ecr_registry ? module.ecr_registry[0].repository_arns : {}
}

output "ecr_docker_login_command" {
  description = "Docker login command for ECR"
  value       = var.enable_eks_module && var.enable_ecr_registry ? module.ecr_registry[0].docker_login_command : null
}

# =============================================================================
# SUMMARY OUTPUTS
# =============================================================================

output "identity_all_roles" {
  description = "Map of all IAM roles created by the identity module"
  value       = var.enable_eks_module ? module.identity[0].all_roles : {}
}

output "identity_all_users" {
  description = "Map of all IAM users created by the identity module"
  value       = var.enable_eks_module ? module.identity[0].all_users : {}
}

# =============================================================================
# EKS MODULE OUTPUTS
# =============================================================================
output "eks_cluster_id" {
  description = "ID of the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_id : null
}

output "eks_cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_arn : null
}

output "eks_cluster_name" {
  description = "Name of the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_name : null
}

output "eks_cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = var.enable_eks_module ? module.eks[0].cluster_endpoint : null
}

output "eks_cluster_version" {
  description = "The Kubernetes server version for the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_version : null
}

output "eks_cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_security_group_id : null
}

output "eks_cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_certificate_authority_data : null
  sensitive   = true
}

output "eks_cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = var.enable_eks_module ? module.eks[0].cluster_oidc_issuer_url : null
}

output "eks_oidc_provider_arn" {
  description = "ARN of the OIDC Provider for IRSA"
  value       = var.enable_eks_module ? module.eks[0].oidc_provider_arn : null
}

output "eks_node_groups" {
  description = "Map of node group attributes"
  value       = var.enable_eks_module ? module.eks[0].node_groups : {}
}

output "eks_cloudwatch_log_group_name" {
  description = "Name of cloudwatch log group for EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cloudwatch_log_group_name : null
}

# =============================================================================
# HELM MODULE OUTPUTS
# =============================================================================

output "helm_metrics_server_status" {
  description = "Status of the metrics server installation"
  value       = var.enable_helm_module ? module.helm[0].metrics_server_status : "not_deployed"
}

output "helm_cluster_autoscaler_status" {
  description = "Status of the cluster autoscaler installation"
  value       = var.enable_helm_module ? module.helm[0].cluster_autoscaler_status : "not_deployed"
}

output "helm_aws_load_balancer_controller_status" {
  description = "Status of the AWS Load Balancer Controller installation"
  value       = var.enable_helm_module ? module.helm[0].aws_load_balancer_controller_status : "not_deployed"
}

output "helm_nginx_ingress_status" {
  description = "Status of the NGINX Ingress Controller installation"
  value       = var.enable_helm_module ? module.helm[0].nginx_ingress_status : "not_deployed"
}

output "helm_cert_manager_status" {
  description = "Status of the Cert Manager installation"
  value       = var.enable_helm_module ? module.helm[0].cert_manager_status : "not_deployed"
}

output "helm_secrets_store_csi_driver_status" {
  description = "Status of the Secrets Store CSI Driver installation"
  value       = var.enable_helm_module ? module.helm[0].secrets_store_csi_driver_status : "not_deployed"
}

# =============================================================================
# EFS OUTPUTS
# =============================================================================

output "efs_file_system_id" {
  description = "ID of the EFS file system"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_file_system_id : null
}

output "efs_file_system_arn" {
  description = "ARN of the EFS file system"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_file_system_arn : null
}

output "efs_file_system_dns_name" {
  description = "DNS name of the EFS file system"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_file_system_dns_name : null
}

output "efs_mount_target_ids" {
  description = "List of EFS mount target IDs"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_mount_target_ids : []
}

output "efs_csi_driver_role_arn" {
  description = "ARN of the EFS CSI Driver IAM role"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_csi_driver_role_arn : null
}

output "efs_storage_class_name" {
  description = "Name of the EFS storage class"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_storage_class_name : null
}

output "efs_csi_driver_status" {
  description = "Status of the EFS CSI Driver installation"
  value       = var.enable_eks_module && var.enable_efs ? module.eks[0].efs_csi_driver_status : "not_enabled"
}

# =============================================================================
# MODULE STATUS SUMMARY
# =============================================================================

output "module_deployment_status" {
  description = "Summary of which modules are deployed"
  value = {
    network_module  = var.enable_network_module
    security_module = var.enable_compute_module  # Security module is deployed with compute
    compute_module  = var.enable_compute_module
    eks_module      = var.enable_eks_module
    helm_module     = var.enable_helm_module
  }
}