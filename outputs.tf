# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.network.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = var.vpc_cidr
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = "arn:aws:ec2:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vpc/${module.network.vpc_id}"
}

output "tgw_attachment_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = module.network.tgw_subnet_ids
}

output "private_eks_subnet_ids" {
  description = "IDs of the private EKS subnets"
  value       = module.network.private_eks_subnet_ids
}

output "private_lb_subnet_ids" {
  description = "IDs of the private load balancer subnets"
  value       = module.network.private_lb_subnet_ids
}

output "intra_subnet_ids" {
  description = "IDs of the intra (isolated) subnets"
  value       = module.network.intra_subnet_ids
}

output "all_subnet_ids" {
  description = "All subnet IDs (TGW + Private EKS + Private LB + Intra)"
  value       = concat(
    module.network.tgw_subnet_ids,
    module.network.private_eks_subnet_ids,
    module.network.private_lb_subnet_ids,
    module.network.intra_subnet_ids
  )
}

output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = module.network.general_use_security_group_id
}

output "bastion_sg_id" {
  description = "ID of the bastion security group"
  value       = module.network.bastion_sg_id
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = var.availability_zones
}

# Transit Gateway Outputs (for existing TGW)
output "existing_transit_gateway_id" {
  description = "ID of the existing Transit Gateway being used"
  value       = var.existing_transit_gateway_id
}

# Bastion Host Module Outputs (Private Infrastructure)
output "bastion_instance_ids" {
  description = "IDs of the bastion host instances"
  value       = var.enable_bastion_host ? module.compute[0].bastion_instance_ids : []
}

output "bastion_private_ips" {
  description = "Private IP addresses of the bastion host instances (accessible via Transit Gateway)"
  value       = var.enable_bastion_host ? module.compute[0].bastion_private_ips : []
}

output "bastion_private_dns" {
  description = "Private DNS names of the bastion host instances"
  value       = var.enable_bastion_host ? module.compute[0].bastion_private_dns : []
}

# EKS Module Outputs
output "eks_cluster_id" {
  description = "ID of the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_id : null
}

output "eks_cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_arn : null
}

output "eks_cluster_name" {
  description = "Name of the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_name : null
}

output "eks_cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = var.enable_eks_module ? module.eks[0].cluster_endpoint : null
}

output "eks_cluster_version" {
  description = "The Kubernetes server version for the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_version : null
}

output "eks_cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_security_group_id : null
}

output "eks_cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = var.enable_eks_module ? module.eks[0].cluster_certificate_authority_data : null
  sensitive   = true
}

output "eks_cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = var.enable_eks_module ? module.eks[0].cluster_oidc_issuer_url : null
}

output "eks_oidc_provider_arn" {
  description = "ARN of the OIDC Provider for IRSA"
  value       = var.enable_eks_module ? module.eks[0].oidc_provider_arn : null
}

output "eks_node_groups" {
  description = "Map of node group attributes"
  value       = var.enable_eks_module ? module.eks[0].node_groups : {}
}

output "eks_cloudwatch_log_group_name" {
  description = "Name of cloudwatch log group for EKS cluster"
  value       = var.enable_eks_module ? module.eks[0].cloudwatch_log_group_name : null
}


