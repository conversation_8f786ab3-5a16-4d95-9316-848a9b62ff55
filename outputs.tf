# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.network.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = var.vpc_cidr
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = "arn:aws:ec2:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vpc/${module.network.vpc_id}"
}

output "tgw_attachment_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = module.network.tgw_subnet_ids
}

output "private_eks_subnet_ids" {
  description = "IDs of the private EKS subnets"
  value       = module.network.private_eks_subnet_ids
}

output "private_lb_subnet_ids" {
  description = "IDs of the private load balancer subnets"
  value       = module.network.private_lb_subnet_ids
}

output "intra_subnet_ids" {
  description = "IDs of the intra (isolated) subnets"
  value       = module.network.intra_subnet_ids
}

output "all_subnet_ids" {
  description = "All subnet IDs (TGW + Private EKS + Private LB + Intra)"
  value       = concat(
    module.network.tgw_subnet_ids,
    module.network.private_eks_subnet_ids,
    module.network.private_lb_subnet_ids,
    module.network.intra_subnet_ids
  )
}

output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = module.network.general_use_security_group_id
}

output "bastion_sg_id" {
  description = "ID of the bastion security group"
  value       = module.network.bastion_sg_id
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = data.aws_availability_zones.available.names
}

# Transit Gateway Outputs (for existing TGW)
output "existing_transit_gateway_id" {
  description = "ID of the existing Transit Gateway being used"
  value       = var.existing_transit_gateway_id
}


