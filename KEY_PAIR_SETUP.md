# EC2 Key Pair Setup Guide for Bastion Host Access

This guide explains how to create and configure EC2 key pairs for secure SSH access to your bastion hosts.

## 🔑 Overview

The bastion hosts require EC2 key pairs for SSH authentication. You'll need to create key pairs in each AWS region where you plan to deploy infrastructure.

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Access to create EC2 key pairs in your target regions

## 🚀 Setup Process

### Step 1: Create Key Pairs

#### Option A: Using AWS CLI (Recommended)

**For Development Environment (eu-west-1):**
```bash
# Create key pair and save private key
aws ec2 create-key-pair \
    --key-name eks-dev-bastion-key \
    --region eu-west-1 \
    --query 'KeyMaterial' \
    --output text > ~/.ssh/eks-dev-bastion-key.pem

# Set proper permissions
chmod 400 ~/.ssh/eks-dev-bastion-key.pem
```

**For Production Environment (us-west-1):**
```bash
# Create key pair and save private key
aws ec2 create-key-pair \
    --key-name eks-prod-bastion-key \
    --region us-west-1 \
    --query 'KeyMaterial' \
    --output text > ~/.ssh/eks-prod-bastion-key.pem

# Set proper permissions
chmod 400 ~/.ssh/eks-prod-bastion-key.pem
```

#### Option B: Using AWS Console

1. **Navigate to EC2 Console** in your target region
2. **Go to Key Pairs** section (under Network & Security)
3. **Click "Create key pair"**
4. **Configure the key pair:**
   - Name: `eks-dev-bastion-key` (for dev) or `eks-prod-bastion-key` (for prod)
   - Key pair type: RSA
   - Private key file format: .pem
5. **Download the private key** and save it securely
6. **Set proper permissions:**
   ```bash
   chmod 400 /path/to/your-key.pem
   ```

### Step 2: Update Terraform Configuration

The key pair names are already configured in your variables, but you need to set the actual key names in your .tfvars files.

**Update dev.tfvars:**
```hcl
# Bastion Host Key Pair
bastion_key_name = "eks-dev-bastion-key"
```

**Update prod.tfvars:**
```hcl
# Bastion Host Key Pair  
bastion_key_name = "eks-prod-bastion-key"
```

### Step 3: Verify Key Pair Configuration

**Check if key pairs exist:**
```bash
# For development region
aws ec2 describe-key-pairs --region eu-west-1 --key-names eks-dev-bastion-key

# For production region
aws ec2 describe-key-pairs --region us-west-1 --key-names eks-prod-bastion-key
```

## 🔐 SSH Access to Bastion Hosts

### Direct SSH Access (via Transit Gateway)

Since your bastion hosts are in private subnets and accessible via Transit Gateway:

```bash
# SSH to development bastion
ssh -i ~/.ssh/eks-dev-bastion-key.pem ubuntu@<bastion-private-ip>

# SSH to production bastion
ssh -i ~/.ssh/eks-prod-bastion-key.pem ubuntu@<bastion-private-ip>
```

### SSH Configuration File

Create an SSH config file for easier access:

```bash
# Create/edit SSH config
nano ~/.ssh/config
```

Add the following configuration:

```
# Development Bastion
Host eks-dev-bastion
    HostName <dev-bastion-private-ip>
    User ubuntu
    IdentityFile ~/.ssh/eks-dev-bastion-key.pem
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null

# Production Bastion
Host eks-prod-bastion
    HostName <prod-bastion-private-ip>
    User ubuntu
    IdentityFile ~/.ssh/eks-prod-bastion-key.pem
    StrictHostKeyChecking no
    UserKnownHostsFile /dev/null
```

Then you can connect simply with:
```bash
ssh eks-dev-bastion
ssh eks-prod-bastion
```

## 🛡️ Security Best Practices

### 1. Key Management
- **Store private keys securely** (never commit to version control)
- **Use different keys** for different environments
- **Rotate keys regularly** (recommended every 90 days)
- **Backup keys securely** in encrypted storage

### 2. Access Control
- **Limit key access** to authorized personnel only
- **Use IAM policies** to control who can create/delete key pairs
- **Monitor key usage** through CloudTrail logs

### 3. Network Security
- **Bastion hosts are private** (no public IPs)
- **Access via Transit Gateway** from your datacenter
- **Security groups** restrict SSH access to specific CIDR ranges

## 🔧 Troubleshooting

### Common Issues

**1. Permission Denied (publickey)**
```bash
# Check key permissions
ls -la ~/.ssh/eks-*-bastion-key.pem
# Should show: -r-------- (400 permissions)

# Fix permissions if needed
chmod 400 ~/.ssh/eks-*-bastion-key.pem
```

**2. Key Pair Not Found**
```bash
# Verify key exists in correct region
aws ec2 describe-key-pairs --region <your-region>
```

**3. Connection Timeout**
- Verify Transit Gateway connectivity
- Check security group rules
- Confirm bastion host is running

### Getting Bastion Host IP Addresses

After deployment, get the private IP addresses:

```bash
# Using Terraform outputs
terraform output bastion_private_ips

# Using AWS CLI
aws ec2 describe-instances \
    --filters "Name=tag:Name,Values=*bastion*" "Name=instance-state-name,Values=running" \
    --query 'Reservations[*].Instances[*].[PrivateIpAddress,Tags[?Key==`Name`].Value|[0]]' \
    --output table
```

## 📝 Next Steps

1. **Create key pairs** in both regions
2. **Update .tfvars files** with key names
3. **Deploy infrastructure** using staged approach
4. **Test SSH connectivity** to bastion hosts
5. **Configure SSH config file** for easier access

## 🔗 Related Documentation

- [SETUP_GUIDE.md](./SETUP_GUIDE.md) - Main infrastructure setup
- [TRANSIT_GATEWAY_SETUP.md](./TRANSIT_GATEWAY_SETUP.md) - Network connectivity
- [modules/compute/README.md](./modules/compute/README.md) - Bastion host details
