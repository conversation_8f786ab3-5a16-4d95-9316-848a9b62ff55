# AWS Region
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "eu-west-1"
}

# Project Name
variable "project_name" {
  description = "Project name for resource naming and tagging"
  type        = string
  default     = "eks-infra"
}

# Environment
variable "environment" {
  description = "Environment name for tagging"
  type        = string
  default     = "dev"
}

# VPC CIDR Block
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "**********/16"
}

# VPC Availability Zones
variable "availability_zones" {
  description = "List of availability zones for subnets"
  type        = list(string)
  default     = ["eu-west-1a", "eu-west-1b"]
}

# Transit Gateway Attachment Subnet CIDRs
variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
  default = [
    "**********/26",  # TGW Subnet 1
    "**********/26"   # TGW Subnet 2
  ]
}

# Private EKS Subnet CIDRs
variable "private_eks_subnet_cidrs" {
  description = "CIDR blocks for private EKS subnets"
  type        = map(string)
  default = {
    az1 = "***********/24"  
    az2 = "***********/24"  
  }
}

# Private Load Balancer Subnet CIDRs
variable "private_lb_subnet_cidrs" {
  description = "CIDR blocks for private load balancer subnets"
  type        = map(string)
  default = {
    az1 = "***********/24" 
    az2 = "***********/24" 
  }
}

# Intra (Isolated) Subnet CIDRs
variable "intra_subnet_cidrs" {
  description = "CIDR blocks for intra (isolated) subnets"
  type        = map(string)
  default = {
    az1 = "***********/24" 
    az2 = "***********/24"  
  }
}

# Map Public IP on Launch
variable "map_public_ip_on_launch" {
  description = "Auto-assign public IP on launch for subnets"
  type        = bool
  default     = false
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
  }
}

# Transit Gateway Configuration
variable "existing_transit_gateway_id" {
  description = "ID of existing Transit Gateway to attach VPC to (if not creating new one)"
  type        = string
  default     = null
}

variable "create_transit_gateway_attachment" {
  description = "Whether to create a Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "enable_dns_support_attachment" {
  description = "Whether DNS support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = true
}

variable "enable_ipv6_support_attachment" {
  description = "Whether IPv6 support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "appliance_mode_support" {
  description = "Whether Appliance Mode support is enabled for the Transit Gateway VPC attachment"
  type        = string
  default     = "disable"
  validation {
    condition     = contains(["enable", "disable"], var.appliance_mode_support)
    error_message = "Appliance mode support must be either 'enable' or 'disable'."
  }
}

# Bastion Host Module Variables
variable "enable_bastion_host" {
  description = "Whether to enable the bastion host module"
  type        = bool
  default     = true
}

variable "bastion_instance_type" {
  description = "EC2 instance type for bastion host"
  type        = string
  default     = "t3.micro"
}

variable "bastion_instance_count" {
  description = "Number of bastion host instances to create"
  type        = number
  default     = 1
}

variable "bastion_key_name" {
  description = "EC2 Key Pair name for bastion host SSH access"
  type        = string
  default     = null
}

variable "bastion_associate_public_ip" {
  description = "Whether to associate a public IP address with bastion host"
  type        = bool
  default     = true
}

variable "bastion_root_volume_size" {
  description = "Size of the root EBS volume for bastion host in GB"
  type        = number
  default     = 10
}

# EKS Module Variables
variable "enable_eks_module" {
  description = "Whether to enable the EKS module"
  type        = bool
  default     = true
}

variable "eks_cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = null
}

variable "eks_cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "eks_endpoint_private_access" {
  description = "Enable private API server endpoint for EKS"
  type        = bool
  default     = true
}

variable "eks_endpoint_public_access" {
  description = "Enable public API server endpoint for EKS"
  type        = bool
  default     = true
}

variable "eks_endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "eks_enable_cluster_log_types" {
  description = "List of control plane logging types to enable for EKS"
  type        = list(string)
  default     = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
}

variable "eks_cluster_log_retention_days" {
  description = "Number of days to retain EKS cluster logs"
  type        = number
  default     = 7
}

variable "eks_node_groups" {
  description = "Map of EKS node group configurations"
  type = map(object({
    instance_types = list(string)
    capacity_type  = string
    min_size       = number
    max_size       = number
    desired_size   = number
    disk_size      = number
    ami_type       = string
    labels         = map(string)
    taints = list(object({
      key    = string
      value  = string
      effect = string
    }))
  }))
  default = {
    general = {
      instance_types = ["t3.medium"]
      capacity_type  = "ON_DEMAND"
      min_size       = 1
      max_size       = 3
      desired_size   = 2
      disk_size      = 20
      ami_type       = "AL2_x86_64"
      labels         = {}
      taints         = []
    }
  }
}

variable "eks_enable_irsa" {
  description = "Enable IAM Roles for Service Accounts for EKS"
  type        = bool
  default     = true
}
