# EKS Module Outputs

output "cluster_id" {
  description = "ID of the EKS cluster"
  value       = aws_eks_cluster.main.id
}

output "cluster_arn" {
  description = "ARN of the EKS cluster"
  value       = aws_eks_cluster.main.arn
}

output "cluster_name" {
  description = "Name of the EKS cluster"
  value       = aws_eks_cluster.main.name
}

output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = aws_eks_cluster.main.endpoint
}

output "cluster_version" {
  description = "The Kubernetes server version for the EKS cluster"
  value       = aws_eks_cluster.main.version
}

output "cluster_platform_version" {
  description = "Platform version for the EKS cluster"
  value       = aws_eks_cluster.main.platform_version
}

output "cluster_status" {
  description = "Status of the EKS cluster"
  value       = aws_eks_cluster.main.status
}

output "cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
}

output "cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = aws_eks_cluster.main.certificate_authority[0].data
}

output "cluster_oidc_issuer_url" {
  description = "The URL on the EKS cluster OIDC Issuer"
  value       = aws_eks_cluster.main.identity[0].oidc[0].issuer
}

output "oidc_provider_arn" {
  description = "ARN of the OIDC Provider for IRSA"
  value       = var.enable_irsa ? aws_iam_openid_connect_provider.eks[0].arn : null
}

output "node_groups" {
  description = "Map of node group attributes"
  value = {
    for k, v in aws_eks_node_group.main : k => {
      arn           = v.arn
      status        = v.status
      capacity_type = v.capacity_type
      instance_types = v.instance_types
      ami_type      = v.ami_type
      node_role_arn = v.node_role_arn
    }
  }
}

output "cluster_primary_security_group_id" {
  description = "The cluster primary security group ID created by EKS"
  value       = aws_eks_cluster.main.vpc_config[0].cluster_security_group_id
}

output "cloudwatch_log_group_name" {
  description = "Name of cloudwatch log group for EKS cluster"
  value       = aws_cloudwatch_log_group.eks.name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of cloudwatch log group for EKS cluster"
  value       = aws_cloudwatch_log_group.eks.arn
}

# Note: User access ARNs are now provided by the Identity module
# Access entries are created in this module but the underlying IAM resources
# are managed by the Identity module

# Addon outputs
output "pod_identity_addon_status" {
  description = "Status of the Pod Identity addon"
  value       = var.enable_pod_identity ? aws_eks_addon.pod_identity[0].status : "not_enabled"
}

output "ebs_csi_driver_addon_status" {
  description = "Status of the EBS CSI Driver addon"
  value       = var.enable_ebs_csi_driver ? aws_eks_addon.ebs_csi_driver[0].status : "not_enabled"
}

# Note: EBS CSI Driver role ARN is now provided by the Identity module

# =============================================================================
# EFS OUTPUTS
# =============================================================================

output "efs_file_system_id" {
  description = "ID of the EFS file system"
  value       = var.enable_efs ? aws_efs_file_system.eks[0].id : null
}

output "efs_file_system_arn" {
  description = "ARN of the EFS file system"
  value       = var.enable_efs ? aws_efs_file_system.eks[0].arn : null
}

output "efs_file_system_dns_name" {
  description = "DNS name of the EFS file system"
  value       = var.enable_efs ? aws_efs_file_system.eks[0].dns_name : null
}

output "efs_mount_target_ids" {
  description = "List of EFS mount target IDs"
  value       = var.enable_efs ? aws_efs_mount_target.eks[*].id : []
}

# Note: EFS CSI Driver role ARN is now provided by the Identity module

output "efs_storage_class_name" {
  description = "Name of the EFS storage class"
  value       = var.enable_efs ? kubernetes_storage_class_v1.efs[0].metadata[0].name : null
}

output "efs_csi_driver_status" {
  description = "Status of the EFS CSI Driver Helm release"
  value       = var.enable_efs ? helm_release.efs_csi_driver[0].status : "not_enabled"
}
