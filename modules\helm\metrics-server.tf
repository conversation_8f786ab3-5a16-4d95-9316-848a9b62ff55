# =============================================================================
# METRICS SERVER HELM CHART
# =============================================================================
# Metrics Server is a scalable, efficient source of container resource metrics
# for Kubernetes built-in autoscaling pipelines. It collects resource metrics
# from Kubelets and exposes them in Kubernetes apiserver through Metrics API
#
# Key Features:
# - Enables Horizontal Pod Autoscaler (HPA)
# - Enables Vertical Pod Autoscaler (VPA)
# - Provides 'kubectl top' functionality
# - Required for cluster autoscaling decisions

# -----------------------------------------------------------------------------
# METRICS SERVER DEPLOYMENT
# -----------------------------------------------------------------------------
resource "helm_release" "metrics_server" {
  count = var.enable_metrics_server ? 1 : 0

  # Basic Helm release configuration
  name       = "metrics-server"
  repository = "https://kubernetes-sigs.github.io/metrics-server/"  # Official Kubernetes SIG repository
  chart      = "metrics-server"
  namespace  = "kube-system"  # Deploy in system namespace alongside other cluster components
  version    = var.metrics_server_version

  # Custom values for production-ready configuration
  values = [
    yamlencode({
      # Command line arguments for metrics-server container
      # These configure how metrics-server communicates with kubelets
      args = [
        "--cert-dir=/tmp",                                              # Directory for TLS certificates
        "--secure-port=4443",                                          # HTTPS port for serving metrics API
        "--kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname",  # Kubelet address resolution order
        "--kubelet-use-node-status-port",                              # Use node status port for kubelet communication
        "--metric-resolution=15s"                                      # How often to collect metrics (15 seconds)
      ]

      # Resource limits and requests for the metrics-server pod
      # These ensure predictable performance and prevent resource starvation
      resources = {
        requests = {
          cpu    = "100m"      # Guaranteed CPU allocation (0.1 CPU cores)
          memory = "200Mi"     # Guaranteed memory allocation (200 MiB)
        }
        limits = {
          cpu    = "200m"      # Maximum CPU usage (0.2 CPU cores)
          memory = "400Mi"     # Maximum memory usage (400 MiB)
        }
      }

      # Node selector ensures metrics-server runs only on Linux nodes
      # This is critical in mixed-OS clusters (Linux + Windows nodes)
      nodeSelector = {
        "kubernetes.io/os" = "linux"
      }

      # Tolerations allow the pod to be scheduled on nodes with specific taints
      # This ensures metrics-server can run on master/control plane nodes
      tolerations = [
        {
          key      = "node-role.kubernetes.io/master"  # Common master node taint
          operator = "Exists"                          # Tolerate if the taint exists (any value)
          effect   = "NoSchedule"                      # Allow scheduling despite NoSchedule effect
        }
      ]

      # Node affinity provides scheduling preferences (not requirements)
      # This prefers to schedule metrics-server on master nodes for better performance
      affinity = {
        nodeAffinity = {
          # Preferred scheduling rules (soft constraints)
          preferredDuringSchedulingIgnoredDuringExecution = [
            {
              weight = 1  # Preference weight (1-100, higher = more preferred)
              preference = {
                matchExpressions = [
                  {
                    key      = "node-role.kubernetes.io/master"  # Look for master node label
                    operator = "Exists"                          # Node has this label (any value)
                  }
                ]
              }
            }
          ]
        }
      }
    })
  ]
}
