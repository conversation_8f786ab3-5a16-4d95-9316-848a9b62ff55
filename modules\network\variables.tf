variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
}

variable "availability_zones" {
  description = "List of availability zones for subnets"
  type        = list(string)
}

variable "private_eks_subnet_cidrs" {
  description = "CIDR blocks for private EKS subnets"
  type        = map(string)
}

variable "private_lb_subnet_cidrs" {
  description = "CIDR blocks for private load balancer subnets"
  type        = map(string)
}

variable "intra_subnet_cidrs" {
  description = "CIDR blocks for intra (isolated) subnets"
  type        = map(string)
}

variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
}

variable "map_public_ip_on_launch" {
  description = "Auto-assign public IP on launch for subnets"
  type        = bool
}

variable "create_transit_gateway_attachment" {
  description = "Whether to create a Transit Gateway VPC attachment"
  type        = bool
}

variable "existing_transit_gateway_id" {
  description = "ID of existing Transit Gateway to attach VPC to (if not creating new one)"
  type        = string
}

variable "project_name" {
  description = "Project name for resource naming and tagging"
  type        = string
}

variable "environment" {
  description = "Environment name for tagging"
  type        = string
}

variable "enable_dns_support_attachment" {
  description = "Whether DNS support is enabled for the Transit Gateway VPC attachment"
  type        = bool
}

variable "enable_ipv6_support_attachment" {
  description = "Whether IPv6 support is enabled for the Transit Gateway VPC attachment"
  type        = bool
}

variable "appliance_mode_support" {
  description = "Whether Appliance Mode support is enabled for the Transit Gateway VPC attachment"
  type        = string
}

variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
  }
} 