# Transit Gateway Attachment Subnet 1a (eu-west-1a)
resource "aws_subnet" "tgw_attachment_1a" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = var.tgw_subnet_cidrs[0]
  availability_zone = var.availability_zones[0] # "eu-west-1a"

  tags = merge(
    var.common_tags,
    {
      Name        = "TGW-${var.project_name}-1a"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Transit Gateway Attachment"
      AZ          = var.availability_zones[0] # "eu-west-1a"
    }
  )
}

# Transit Gateway Attachment Subnet 1b (eu-west-1b)
resource "aws_subnet" "tgw_attachment_1b" {
  vpc_id            = aws_vpc.main.id
  cidr_block        = var.tgw_subnet_cidrs[1]
  availability_zone = var.availability_zones[1] # "eu-west-1b"

  tags = merge(
    var.common_tags,
    {
      Name        = "TGW-${var.project_name}-1b"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Transit Gateway Attachment"
      AZ          = var.availability_zones[1] # "eu-west-1b"
    }
  )
}

# Data source to get existing Transit Gateway information
data "aws_ec2_transit_gateway" "existing" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0
  id    = var.existing_transit_gateway_id
}

# Transit Gateway VPC Attachment to existing Transit Gateway
resource "aws_ec2_transit_gateway_vpc_attachment" "main" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  subnet_ids         = [
    aws_subnet.tgw_attachment_1a.id,
    aws_subnet.tgw_attachment_1b.id
  ]
  transit_gateway_id = var.existing_transit_gateway_id
  vpc_id             = aws_vpc.main.id

  # Configuration options
  dns_support                                    = var.enable_dns_support_attachment ? "enable" : "disable"
  ipv6_support                                   = var.enable_ipv6_support_attachment ? "enable" : "disable"
  appliance_mode_support                         = var.appliance_mode_support

  tags = merge(
    var.common_tags,
    {
      Name                = "${var.project_name}-tgw-vpc-attachment"
      Environment         = var.environment
      Project             = var.project_name
      VPC                 = aws_vpc.main.id
      TransitGatewayId    = var.existing_transit_gateway_id
      AttachmentType      = "VPC"
    }
  )

  depends_on = [
    aws_subnet.tgw_attachment_1a,
    aws_subnet.tgw_attachment_1b
  ]
}
