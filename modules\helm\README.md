# Helm Module

This module manages Helm chart installations for EKS clusters. It provides a standardized way to deploy common Kubernetes applications and tools.

## File Structure

- **providers.tf**: Helm and Kubernetes provider configuration
- **metrics-server.tf**: Metrics Server installation
- **cluster-autoscaler.tf**: Cluster Autoscaler installation
- **aws-load-balancer-controller.tf**: AWS Load Balancer Controller installation
- **variables.tf**: Input variables
- **outputs.tf**: Output values

## Supported Applications

### Core Components
- **Metrics Server**: Provides resource usage metrics for pods and nodes
- **Cluster Autoscaler**: Automatically scales node groups based on pod resource requirements

### Networking & Load Balancing
- **AWS Load Balancer Controller**: Manages AWS Application Load Balancers and Network Load Balancers
- **NGINX Ingress Controller**: Alternative ingress controller (optional)

### Security & Secrets
- **Cert Manager**: Automatic TLS certificate management (optional)
- **Secrets Store CSI Driver**: Integration with AWS Secrets Manager and Parameter Store (optional)

### Storage
- **EFS CSI Driver**: Amazon EFS integration for persistent storage (optional)

## Usage

```hcl
module "helm" {
  source = "./modules/helm"

  # Cluster Configuration
  cluster_name               = module.eks.cluster_name
  cluster_endpoint           = module.eks.cluster_endpoint
  cluster_ca_certificate     = module.eks.cluster_certificate_authority_data
  cluster_token              = data.aws_eks_cluster_auth.main.token
  
  # AWS Configuration
  vpc_id     = module.vpc.vpc_id
  aws_region = var.aws_region

  # Project Configuration
  project_name = var.project_name
  environment  = var.environment
  common_tags  = var.common_tags

  # Core Components (recommended)
  enable_metrics_server     = true
  enable_cluster_autoscaler = true

  # Load Balancing
  enable_aws_load_balancer_controller = true
  enable_nginx_ingress                = false

  # Security & Secrets (optional)
  enable_cert_manager                    = false
  enable_secrets_store_csi_driver        = false

  # Storage (optional)
  enable_efs_csi_driver = false

  # IAM Role ARNs (required for some components)
  cluster_autoscaler_role_arn         = module.iam_roles.cluster_autoscaler_role_arn
  aws_load_balancer_controller_role_arn = module.iam_roles.aws_load_balancer_controller_role_arn
  efs_csi_driver_role_arn             = module.iam_roles.efs_csi_driver_role_arn

  depends_on = [module.eks]
}
```

## Authentication Token

The module requires an authentication token to connect to the EKS cluster. You can obtain this using:

```hcl
data "aws_eks_cluster_auth" "main" {
  name = module.eks.cluster_name
}
```

## IAM Roles

Some Helm charts require IAM roles with specific permissions. These should be created separately and passed to the module:

### Cluster Autoscaler
Requires permissions to describe and modify Auto Scaling Groups.

### AWS Load Balancer Controller
Requires permissions to manage ALBs, NLBs, and related resources.

### EFS CSI Driver
Requires permissions to manage EFS file systems and access points.

## Component Details

### Metrics Server
- Deployed to `kube-system` namespace
- Provides CPU and memory metrics for HPA and VPA
- Configured with resource limits and node affinity

### Cluster Autoscaler
- Deployed to `kube-system` namespace
- Automatically discovers node groups using tags
- Configured with least-waste expander strategy

### AWS Load Balancer Controller
- Deployed to `kube-system` namespace
- Manages ALB and NLB resources
- Supports both public and private load balancers

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| helm | >= 2.12 |
| kubernetes | >= 2.24 |

## Providers

| Name | Version |
|------|---------|
| helm | >= 2.12 |
| kubernetes | >= 2.24 |

## Inputs

See `variables.tf` for a complete list of input variables.

## Outputs

See `outputs.tf` for installation status of each component.

## Notes

- All charts are installed with resource limits and requests
- Node selectors ensure pods run on Linux nodes
- Tolerations allow scheduling on master/control plane nodes where appropriate
- Charts are configured with production-ready defaults
- Version pinning ensures consistent deployments
